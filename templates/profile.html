<!DOCTYPE html>
<html lang="es">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>VecinoSeguro - Mi Perfil</title>
    <link
      href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap"
      rel="stylesheet"
    />
    <link
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
      rel="stylesheet"
    />
    <style>
      :root {
        --primary-color: #4f46e5;
        --primary-dark: #3730a3;
        --secondary-color: #06b6d4;
        --success-color: #10b981;
        --warning-color: #f59e0b;
        --danger-color: #ef4444;
        --gray-50: #f9fafb;
        --gray-100: #f3f4f6;
        --gray-200: #e5e7eb;
        --gray-300: #d1d5db;
        --gray-400: #9ca3af;
        --gray-500: #6b7280;
        --gray-600: #4b5563;
        --gray-700: #374151;
        --gray-800: #1f2937;
        --gray-900: #111827;
      }

      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: "Inter", sans-serif;
        background: var(--gray-50);
        color: var(--gray-900);
        line-height: 1.6;
      }

      .navbar {
        background: white;
        box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
        padding: 1rem 0;
        margin-bottom: 2rem;
        border-radius: 8px;
        position: sticky;
        top: 0;
        z-index: 100;
      }

      .navbar-content {
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 1rem;
        display: flex;
        justify-content: space-between;
        align-items: center;
      }

      .logo {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-size: 1.5rem;
        font-weight: 700;
        color: #4f46e5;
        text-decoration: none;
      }

      .nav-links {
        display: flex;
        gap: 2rem;
        align-items: center;
      }

      .nav-links a {
        text-decoration: none;
        color: #6b7280;
        font-weight: 500;
        padding: 0.5rem 1rem;
        border-radius: 0.5rem;
        transition: all 0.2s;
      }

      .nav-links a:hover,
      .nav-links a.active {
        color: #4f46e5;
        background: #f9fafb;
      }

      .user-menu {
        display: flex;
        align-items: center;
        gap: 1rem;
      }

      .user-menu .nav-links {
        display: flex;
        gap: 1rem;
        align-items: center;
      }

      .user-button {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        background: none;
        border: none;
        cursor: pointer;
        padding: 0.5rem 1rem;
        border-radius: 0.5rem;
        transition: all 0.2s;
        color: #6b7280;
        font-weight: 500;
      }

      .user-button:hover {
        background: #f9fafb;
        color: #4f46e5;
      }

      .user-button:hover {
        background: var(--gray-100);
      }

      /* Mobile Navigation */
      .mobile-menu-toggle {
        display: none;
        background: none;
        border: none;
        cursor: pointer;
        padding: 0.5rem;
        color: #6b7280;
        font-size: 1.25rem;
      }

      .container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 1.5rem;
      }

      .page-header {
        margin-bottom: 2rem;
      }

      .page-title {
        font-size: 2rem;
        font-weight: 700;
        color: var(--gray-900);
        margin-bottom: 0.5rem;
      }

      .page-subtitle {
        color: var(--gray-600);
        font-size: 1.1rem;
      }

      .profile-grid {
        display: grid;
        grid-template-columns: 1fr 2fr;
        gap: 2rem;
        margin-bottom: 2rem;
      }

      .profile-card {
        background: white;
        border-radius: 1rem;
        padding: 2rem;
        box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
      }

      .profile-avatar {
        text-align: center;
        margin-bottom: 2rem;
      }

      .avatar {
        width: 120px;
        height: 120px;
        border-radius: 50%;
        background: linear-gradient(
          135deg,
          var(--primary-color),
          var(--secondary-color)
        );
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 1rem;
        font-size: 3rem;
        color: white;
        font-weight: 600;
      }

      .profile-name {
        font-size: 1.5rem;
        font-weight: 600;
        margin-bottom: 0.5rem;
      }

      .profile-email {
        color: var(--gray-600);
        margin-bottom: 1rem;
      }

      .profile-stats {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
        margin-top: 2rem;
      }

      .stat-item {
        text-align: center;
        padding: 1rem;
        background: var(--gray-50);
        border-radius: 0.5rem;
      }

      .stat-number {
        font-size: 1.5rem;
        font-weight: 700;
        color: var(--primary-color);
      }

      .stat-label {
        font-size: 0.875rem;
        color: var(--gray-600);
        margin-top: 0.25rem;
      }

      .section-title {
        font-size: 1.25rem;
        font-weight: 600;
        margin-bottom: 1.5rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
      }

      .form-group {
        margin-bottom: 1.5rem;
      }

      .form-row {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
      }

      .form-group label {
        display: block;
        font-weight: 500;
        color: var(--gray-700);
        margin-bottom: 0.5rem;
        font-size: 0.875rem;
      }

      .form-group input {
        width: 100%;
        padding: 0.75rem 1rem;
        border: 2px solid var(--gray-200);
        border-radius: 0.5rem;
        font-size: 1rem;
        transition: all 0.2s;
      }

      .form-group input:focus {
        outline: none;
        border-color: var(--primary-color);
        box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
      }

      .btn {
        padding: 0.75rem 1.5rem;
        border: none;
        border-radius: 0.5rem;
        font-size: 0.875rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.2s;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
      }

      .btn-primary {
        background: var(--primary-color);
        color: white;
      }

      .btn-primary:hover {
        background: var(--primary-dark);
      }

      .btn-secondary {
        background: var(--gray-200);
        color: var(--gray-700);
      }

      .btn-secondary:hover {
        background: var(--gray-300);
      }

      .btn-danger {
        background: var(--danger-color);
        color: white;
      }

      .btn-danger:hover {
        background: #dc2626;
      }

      .btn-success {
        background: var(--success-color);
        color: white;
      }

      .btn-success:hover {
        background: #059669;
      }

      .vehicles-section,
      .locations-section {
        background: white;
        border-radius: 1rem;
        padding: 2rem;
        box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
        margin-bottom: 2rem;
      }

      .vehicle-item,
      .location-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 1rem;
        border: 2px solid var(--gray-200);
        border-radius: 0.5rem;
        margin-bottom: 1rem;
      }

      .vehicle-info,
      .location-info {
        flex: 1;
      }

      .vehicle-plate {
        font-size: 1.125rem;
        font-weight: 600;
        color: var(--gray-900);
      }

      .vehicle-details {
        color: var(--gray-600);
        font-size: 0.875rem;
        margin-top: 0.25rem;
      }

      .vehicle-actions,
      .location-actions {
        display: flex;
        gap: 0.5rem;
      }

      .badge {
        padding: 0.25rem 0.75rem;
        border-radius: 9999px;
        font-size: 0.75rem;
        font-weight: 500;
      }

      .badge-success {
        background: #dcfce7;
        color: #166534;
      }

      .badge-warning {
        background: #fef3c7;
        color: #92400e;
      }

      .badge-danger {
        background: #fee2e2;
        color: #991b1b;
      }

      .alert {
        padding: 1rem;
        border-radius: 0.5rem;
        margin-bottom: 1.5rem;
        font-size: 0.875rem;
      }

      .alert-success {
        background: #f0fdf4;
        color: #166534;
        border: 1px solid #bbf7d0;
      }

      .alert-error {
        background: #fef2f2;
        color: #991b1b;
        border: 1px solid #fecaca;
      }

      .modal {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        z-index: 1000;
      }

      .modal.show {
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .modal-content {
        background: white;
        border-radius: 1rem;
        padding: 2rem;
        width: 90%;
        max-width: 500px;
        max-height: 90vh;
        overflow-y: auto;
      }

      .modal-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1.5rem;
      }

      .modal-title {
        font-size: 1.25rem;
        font-weight: 600;
      }

      .close-btn {
        background: none;
        border: none;
        font-size: 1.5rem;
        cursor: pointer;
        color: var(--gray-400);
      }

      .loading {
        display: none;
      }

      .loading.show {
        display: inline-block;
        animation: spin 1s linear infinite;
      }

      @keyframes spin {
        0% {
          transform: rotate(0deg);
        }
        100% {
          transform: rotate(360deg);
        }
      }

      @media (max-width: 768px) {
        .container {
          padding: 1rem;
        }

        .profile-grid {
          grid-template-columns: 1fr;
        }

        .form-row {
          grid-template-columns: 1fr;
        }

        .user-menu .nav-links {
          display: none;
        }

        .mobile-menu-toggle {
          display: block;
        }

        .navbar-content {
          padding: 0 1rem;
        }
      }
    </style>
  </head>
  <body>
    <!-- Navigation -->
    <nav class="navbar">
      <div class="navbar-content">
        <div class="logo">
          <i class="fas fa-shield-alt"></i>
          VecinoSeguro
        </div>
        <div class="user-menu">
          <div class="nav-links">
            <a href="/">Dashboard</a>
            <a href="/profile" class="active">Mi Perfil</a>
          </div>
          <button class="mobile-menu-toggle" onclick="toggleMobileMenu()">
            <i class="fas fa-bars"></i>
          </button>
        </div>
      </div>
    </nav>

    <div class="container">
      <!-- Page Header -->
      <div class="page-header">
        <h1 class="page-title">Mi Perfil</h1>
        <p class="page-subtitle">
          Gestiona tu información personal, vehículos y ubicaciones
        </p>
      </div>

      <!-- Alert Container -->
      <div id="alert-container"></div>

      <!-- Profile Grid -->
      <div class="profile-grid">
        <!-- Profile Summary -->
        <div class="profile-card">
          <div class="profile-avatar">
            <div class="avatar" id="userAvatar">
              <i class="fas fa-user"></i>
            </div>
            <div class="profile-name" id="userName">Cargando...</div>
            <div class="profile-email" id="userEmail">Cargando...</div>
            <span class="badge badge-success" id="userStatus">Activo</span>
          </div>

          <div class="profile-stats">
            <div class="stat-item">
              <div class="stat-number" id="vehicleCount">0</div>
              <div class="stat-label">Vehículos</div>
            </div>
            <div class="stat-item">
              <div class="stat-number" id="locationCount">0</div>
              <div class="stat-label">Ubicaciones</div>
            </div>
          </div>

          <!-- Logout Button -->
          <div style="margin-top: 2rem; text-align: center">
            <button
              class="btn btn-danger"
              onclick="logout()"
              style="width: 100%"
            >
              <i class="fas fa-sign-out-alt"></i>
              Cerrar Sesión
            </button>
          </div>
        </div>

        <!-- Profile Form -->
        <div class="profile-card">
          <h3 class="section-title">
            <i class="fas fa-user-edit"></i>
            Información Personal
          </h3>

          <form id="profileForm">
            <div class="form-row">
              <div class="form-group">
                <label for="first_name">Nombre</label>
                <input type="text" id="first_name" name="first_name" required />
              </div>
              <div class="form-group">
                <label for="last_name">Apellido</label>
                <input type="text" id="last_name" name="last_name" required />
              </div>
            </div>

            <div class="form-group">
              <label for="email">Correo Electrónico</label>
              <input type="email" id="email" name="email" disabled />
            </div>

            <div class="form-group">
              <label for="phone">Teléfono</label>
              <input type="tel" id="phone" name="phone" />
            </div>

            <div style="display: flex; gap: 1rem">
              <button type="submit" class="btn btn-primary">
                <i class="fas fa-spinner loading" id="profileSpinner"></i>
                <span id="profileBtnText">Guardar Cambios</span>
              </button>
              <button
                type="button"
                class="btn btn-secondary"
                onclick="showPasswordModal()"
              >
                <i class="fas fa-key"></i>
                Cambiar Contraseña
              </button>
            </div>
          </form>
        </div>
      </div>

      <!-- Vehicles Section -->
      <div class="vehicles-section">
        <div
          style="
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
          "
        >
          <h3 class="section-title" style="margin-bottom: 0">
            <i class="fas fa-car"></i>
            Mis Vehículos
          </h3>
          <button class="btn btn-primary" onclick="showVehicleModal()">
            <i class="fas fa-plus"></i>
            Agregar Vehículo
          </button>
        </div>

        <div id="vehiclesList">
          <div
            style="text-align: center; padding: 2rem; color: var(--gray-500)"
          >
            <i
              class="fas fa-car"
              style="font-size: 3rem; margin-bottom: 1rem; opacity: 0.3"
            ></i>
            <p>Cargando vehículos...</p>
          </div>
        </div>
      </div>

      <!-- Locations Section -->
      <div class="locations-section">
        <h3 class="section-title">
          <i class="fas fa-map-marker-alt"></i>
          Mis Ubicaciones
        </h3>

        <div id="locationsList">
          <div
            style="text-align: center; padding: 2rem; color: var(--gray-500)"
          >
            <i
              class="fas fa-map-marker-alt"
              style="font-size: 3rem; margin-bottom: 1rem; opacity: 0.3"
            ></i>
            <p>Cargando ubicaciones...</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Vehicle Modal -->
    <div class="modal" id="vehicleModal">
      <div class="modal-content">
        <div class="modal-header">
          <h3 class="modal-title" id="vehicleModalTitle">Agregar Vehículo</h3>
          <button class="close-btn" onclick="closeVehicleModal()">
            &times;
          </button>
        </div>

        <form id="vehicleForm">
          <input type="hidden" id="vehicleId" name="vehicleId" />

          <div class="form-group">
            <label for="license_plate">Placa *</label>
            <input
              type="text"
              id="license_plate"
              name="license_plate"
              required
              style="text-transform: uppercase"
            />
          </div>

          <div class="form-row">
            <div class="form-group">
              <label for="make">Marca</label>
              <input type="text" id="make" name="make" />
            </div>
            <div class="form-group">
              <label for="model">Modelo</label>
              <input type="text" id="model" name="model" />
            </div>
          </div>

          <div class="form-row">
            <div class="form-group">
              <label for="year">Año</label>
              <input
                type="number"
                id="year"
                name="year"
                min="1900"
                max="2030"
              />
            </div>
            <div class="form-group">
              <label for="color">Color</label>
              <input type="text" id="color" name="color" />
            </div>
          </div>

          <div class="form-group">
            <label for="vehicle_type">Tipo de Vehículo</label>
            <select
              id="vehicle_type"
              name="vehicle_type"
              style="
                width: 100%;
                padding: 0.75rem 1rem;
                border: 2px solid var(--gray-200);
                border-radius: 0.5rem;
              "
            >
              <option value="car">Automóvil</option>
              <option value="motorcycle">Motocicleta</option>
              <option value="truck">Camioneta</option>
              <option value="van">Van</option>
              <option value="suv">SUV</option>
            </select>
          </div>

          <div style="display: flex; gap: 1rem; margin-top: 2rem">
            <button type="submit" class="btn btn-success">
              <i class="fas fa-spinner loading" id="vehicleSpinner"></i>
              <span id="vehicleBtnText">Guardar Vehículo</span>
            </button>
            <button
              type="button"
              class="btn btn-secondary"
              onclick="closeVehicleModal()"
            >
              Cancelar
            </button>
          </div>
        </form>
      </div>
    </div>

    <!-- Password Modal -->
    <div class="modal" id="passwordModal">
      <div class="modal-content">
        <div class="modal-header">
          <h3 class="modal-title">Cambiar Contraseña</h3>
          <button class="close-btn" onclick="closePasswordModal()">
            &times;
          </button>
        </div>

        <form id="passwordForm">
          <div class="form-group">
            <label for="current_password">Contraseña Actual *</label>
            <input
              type="password"
              id="current_password"
              name="current_password"
              required
            />
          </div>

          <div class="form-group">
            <label for="new_password">Nueva Contraseña *</label>
            <input
              type="password"
              id="new_password"
              name="new_password"
              required
            />
          </div>

          <div class="form-group">
            <label for="confirm_password">Confirmar Nueva Contraseña *</label>
            <input
              type="password"
              id="confirm_password"
              name="confirm_password"
              required
            />
          </div>

          <div style="display: flex; gap: 1rem; margin-top: 2rem">
            <button type="submit" class="btn btn-primary">
              <i class="fas fa-spinner loading" id="passwordSpinner"></i>
              <span id="passwordBtnText">Cambiar Contraseña</span>
            </button>
            <button
              type="button"
              class="btn btn-secondary"
              onclick="closePasswordModal()"
            >
              Cancelar
            </button>
          </div>
        </form>
      </div>
    </div>

    <script src="/static/js/profile.js?v=2.0"></script>
  </body>
</html>
