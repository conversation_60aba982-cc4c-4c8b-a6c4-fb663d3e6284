<!DOCTYPE html>
<html lang="es">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Gestión de Usuarios - VecinoSeguro</title>
    <link
      rel="stylesheet"
      href="{{ url_for('static', filename='css/style.css') }}"
    />
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css"
    />
    <style>
      /* Admin Users specific styles */
      body {
        background-color: var(--bg-secondary);
        margin: 0;
        padding: 0;
        min-height: 100vh;
        display: flex;
        flex-direction: column;
      }

      .admin-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
      }

      .admin-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 30px;
        padding-bottom: 20px;
        border-bottom: 2px solid #e0e0e0;
      }

      .admin-title {
        color: #2c3e50;
        margin: 0;
      }

      .btn-create-user {
        background: #27ae60;
        color: white;
        padding: 12px 24px;
        border: none;
        border-radius: 8px;
        cursor: pointer;
        font-size: 16px;
        display: flex;
        align-items: center;
        gap: 8px;
        transition: background 0.3s;
      }

      .btn-create-user:hover {
        background: #219a52;
      }

      .search-filters {
        display: flex;
        gap: 15px;
        margin-bottom: 20px;
        flex-wrap: wrap;
        align-items: center;
      }

      .search-input {
        flex: 1;
        min-width: 250px;
        padding: 12px;
        border: 1px solid #ddd;
        border-radius: 8px;
        font-size: 16px;
      }

      .filter-select {
        padding: 12px;
        border: 1px solid #ddd;
        border-radius: 8px;
        font-size: 16px;
        background: white;
      }

      .users-table {
        background: white;
        border-radius: 12px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        overflow: hidden;
      }

      .table {
        width: 100%;
        border-collapse: collapse;
      }

      .table th,
      .table td {
        padding: 15px;
        text-align: left;
        border-bottom: 1px solid #e0e0e0;
      }

      .table th {
        background: #f8f9fa;
        font-weight: 600;
        color: #2c3e50;
      }

      .table tbody tr:hover {
        background: #f8f9fa;
      }

      .user-avatar {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: #3498db;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: bold;
        margin-right: 10px;
      }

      .user-info {
        display: flex;
        align-items: center;
      }

      .user-details {
        display: flex;
        flex-direction: column;
      }

      .user-name {
        font-weight: 600;
        color: #2c3e50;
      }

      .user-email {
        color: #7f8c8d;
        font-size: 14px;
      }

      .status-badge {
        padding: 4px 12px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: 600;
        text-transform: uppercase;
      }

      .status-active {
        background: #d4edda;
        color: #155724;
      }

      .status-inactive {
        background: #f8d7da;
        color: #721c24;
      }

      .role-badge {
        padding: 4px 12px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: 600;
        text-transform: uppercase;
      }

      .role-admin {
        background: #fff3cd;
        color: #856404;
      }

      .role-user {
        background: #d1ecf1;
        color: #0c5460;
      }

      .action-buttons {
        display: flex;
        gap: 8px;
      }

      .btn-action {
        padding: 6px 12px;
        border: none;
        border-radius: 6px;
        cursor: pointer;
        font-size: 12px;
        transition: all 0.3s;
      }

      .btn-edit {
        background: #3498db;
        color: white;
      }

      .btn-edit:hover {
        background: #2980b9;
      }

      .btn-toggle {
        background: #f39c12;
        color: white;
      }

      .btn-toggle:hover {
        background: #e67e22;
      }

      .btn-delete {
        background: #e74c3c;
        color: white;
      }

      .btn-delete:hover {
        background: #c0392b;
      }

      .btn-promote {
        background: #27ae60;
        color: white;
      }

      .btn-promote:hover {
        background: #219a52;
      }

      .btn-demote {
        background: #e67e22;
        color: white;
      }

      .btn-demote:hover {
        background: #d35400;
      }

      .pagination {
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 10px;
        margin-top: 20px;
      }

      .pagination button {
        padding: 8px 12px;
        border: 1px solid #ddd;
        background: white;
        cursor: pointer;
        border-radius: 6px;
      }

      .pagination button:hover:not(:disabled) {
        background: #f8f9fa;
      }

      .pagination button:disabled {
        opacity: 0.5;
        cursor: not-allowed;
      }

      .pagination .current-page {
        background: #3498db;
        color: white;
        border-color: #3498db;
      }

      .loading {
        text-align: center;
        padding: 40px;
        color: #7f8c8d;
      }

      .empty-state {
        text-align: center;
        padding: 60px 20px;
        color: #7f8c8d;
      }

      .empty-state i {
        font-size: 48px;
        margin-bottom: 20px;
        opacity: 0.5;
      }

      /* Modal styles */
      .modal {
        display: none;
        position: fixed;
        z-index: 1000;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
      }

      .modal-content {
        background-color: white;
        margin: 5% auto;
        padding: 0;
        border-radius: 12px;
        width: 90%;
        max-width: 600px;
        max-height: 90vh;
        overflow-y: auto;
      }

      .modal-header {
        padding: 20px;
        border-bottom: 1px solid #e0e0e0;
        display: flex;
        justify-content: space-between;
        align-items: center;
      }

      .modal-title {
        margin: 0;
        color: #2c3e50;
      }

      .close {
        color: #aaa;
        font-size: 28px;
        font-weight: bold;
        cursor: pointer;
      }

      .close:hover {
        color: #000;
      }

      .modal-body {
        padding: 20px;
      }

      .form-group {
        margin-bottom: 20px;
      }

      .form-label {
        display: block;
        margin-bottom: 5px;
        font-weight: 600;
        color: #2c3e50;
      }

      .form-input {
        width: 100%;
        padding: 12px;
        border: 1px solid #ddd;
        border-radius: 8px;
        font-size: 16px;
        box-sizing: border-box;
      }

      .form-checkbox {
        display: flex;
        align-items: center;
        gap: 8px;
      }

      .form-checkbox input {
        width: auto;
      }

      .modal-footer {
        padding: 20px;
        border-top: 1px solid #e0e0e0;
        display: flex;
        justify-content: flex-end;
        gap: 10px;
      }

      .btn-cancel {
        background: #6c757d;
        color: white;
        padding: 10px 20px;
        border: none;
        border-radius: 6px;
        cursor: pointer;
      }

      .btn-save {
        background: #28a745;
        color: white;
        padding: 10px 20px;
        border: none;
        border-radius: 6px;
        cursor: pointer;
      }

      @media (max-width: 768px) {
        .admin-header {
          flex-direction: column;
          gap: 15px;
          align-items: stretch;
        }

        .search-filters {
          flex-direction: column;
        }

        .search-input {
          min-width: auto;
        }

        .table {
          font-size: 14px;
        }

        .table th,
        .table td {
          padding: 10px 8px;
        }

        .action-buttons {
          flex-direction: column;
        }
      }
    </style>
  </head>
  <body>
    <!-- Navigation -->
    <nav class="navbar">
      <div class="nav-container">
        <div class="nav-logo">
          <h2>VecinoSeguro</h2>
        </div>

        <div class="user-menu">
          <div class="nav-links">
            <a href="/" class="nav-link">Dashboard</a>
            <a href="/profile" class="nav-link">Mi Perfil</a>
            <a href="/admin/dashboard" class="nav-link">Admin</a>
            <a href="/admin/users" class="nav-link active">Usuarios</a>
          </div>
          <div class="hamburger-menu">
            <i class="fas fa-bars"></i>
          </div>
          <button class="user-button" onclick="logout()">
            <i class="fas fa-sign-out-alt"></i>
            Cerrar Sesión
          </button>
        </div>
      </div>
    </nav>

    <!-- Main Content -->
    <div class="admin-container">
      <div class="admin-header">
        <h1 class="admin-title">
          <i class="fas fa-users"></i>
          Gestión de Usuarios
        </h1>
        <button class="btn-create-user" onclick="openCreateUserModal()">
          <i class="fas fa-plus"></i>
          Crear Usuario
        </button>
      </div>

      <!-- Search and Filters -->
      <div class="search-filters">
        <input
          type="text"
          class="search-input"
          id="searchInput"
          placeholder="Buscar por nombre o email..."
        />
        <select class="filter-select" id="statusFilter">
          <option value="">Todos los estados</option>
          <option value="active">Activos</option>
          <option value="inactive">Inactivos</option>
        </select>
        <select class="filter-select" id="roleFilter">
          <option value="">Todos los roles</option>
          <option value="admin">Administradores</option>
          <option value="user">Usuarios</option>
        </select>
      </div>

      <!-- Users Table -->
      <div class="users-table">
        <div id="loadingIndicator" class="loading">
          <i class="fas fa-spinner fa-spin"></i>
          Cargando usuarios...
        </div>

        <div id="usersTableContainer" style="display: none">
          <table class="table">
            <thead>
              <tr>
                <th>Usuario</th>
                <th>Teléfono</th>
                <th>Estado</th>
                <th>Rol</th>
                <th>Fecha Registro</th>
                <th>Acciones</th>
              </tr>
            </thead>
            <tbody id="usersTableBody">
              <!-- Users will be loaded here -->
            </tbody>
          </table>
        </div>

        <div id="emptyState" class="empty-state" style="display: none">
          <i class="fas fa-users"></i>
          <h3>No se encontraron usuarios</h3>
          <p>No hay usuarios que coincidan con los filtros seleccionados.</p>
        </div>
      </div>

      <!-- Pagination -->
      <div class="pagination" id="pagination" style="display: none">
        <button id="prevPage" onclick="changePage(-1)">
          <i class="fas fa-chevron-left"></i>
        </button>
        <span id="pageInfo">Página 1 de 1</span>
        <button id="nextPage" onclick="changePage(1)">
          <i class="fas fa-chevron-right"></i>
        </button>
      </div>
    </div>

    <!-- Create/Edit User Modal -->
    <div id="userModal" class="modal">
      <div class="modal-content">
        <div class="modal-header">
          <h2 class="modal-title" id="modalTitle">Crear Usuario</h2>
          <span class="close" onclick="closeUserModal()">&times;</span>
        </div>
        <div class="modal-body">
          <form id="userForm">
            <div class="form-group">
              <label class="form-label" for="userEmail">Email *</label>
              <input type="email" class="form-input" id="userEmail" required />
            </div>
            <div class="form-group">
              <label class="form-label" for="userFirstName">Nombre *</label>
              <input
                type="text"
                class="form-input"
                id="userFirstName"
                required
              />
            </div>
            <div class="form-group">
              <label class="form-label" for="userLastName">Apellido *</label>
              <input
                type="text"
                class="form-input"
                id="userLastName"
                required
              />
            </div>
            <div class="form-group">
              <label class="form-label" for="userPhone">Teléfono</label>
              <input type="tel" class="form-input" id="userPhone" />
            </div>
            <div class="form-group">
              <label class="form-label" for="userPassword">Contraseña *</label>
              <input
                type="password"
                class="form-input"
                id="userPassword"
                required
              />
            </div>
            <div class="form-group">
              <div class="form-checkbox">
                <input type="checkbox" id="userIsActive" checked />
                <label for="userIsActive">Usuario activo</label>
              </div>
            </div>
            <div class="form-group">
              <div class="form-checkbox">
                <input type="checkbox" id="userIsAdmin" />
                <label for="userIsAdmin">Administrador</label>
              </div>
            </div>
            <div class="form-group">
              <div class="form-checkbox">
                <input type="checkbox" id="userEmailVerified" checked />
                <label for="userEmailVerified">Email verificado</label>
              </div>
            </div>
          </form>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn-cancel" onclick="closeUserModal()">
            Cancelar
          </button>
          <button type="button" class="btn-save" onclick="saveUser()">
            Guardar
          </button>
        </div>
      </div>
    </div>

    <script src="/static/js/admin_users.js?v=1.0"></script>
  </body>
</html>
