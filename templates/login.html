<!DOCTYPE html>
<html lang="es">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>VecinoSeguro - Iniciar <PERSON></title>
    <link
      href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap"
      rel="stylesheet"
    />
    <link
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
      rel="stylesheet"
    />
    <link
      rel="stylesheet"
      href="{{ url_for('static', filename='css/style.css') }}"
    />
    <style>
      /* Login-specific styles */
      body {
        background: linear-gradient(
          135deg,
          var(--primary-color) 0%,
          var(--secondary-color) 100%
        );
        min-height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: var(--space-lg);
      }

      .login-container {
        background: var(--bg-primary);
        border-radius: var(--radius-xl);
        box-shadow: var(--shadow-xl);
        padding: var(--space-2xl);
        width: 100%;
        max-width: 400px;
      }

      .logo {
        text-align: center;
        margin-bottom: var(--space-2xl);
      }

      .logo i {
        font-size: 3rem;
        color: var(--primary-color);
        margin-bottom: var(--space-md);
      }

      .logo h1 {
        color: var(--text-primary);
        margin-bottom: var(--space-sm);
      }

      .logo p {
        color: var(--text-secondary);
        margin: 0;
      }

      .form-group {
        position: relative;
      }

      .form-input {
        padding-left: 2.5rem;
      }

      .input-icon {
        position: absolute;
        left: var(--space-md);
        top: 50%;
        transform: translateY(-50%);
        color: var(--text-muted);
        pointer-events: none;
      }

      .form-footer {
        text-align: center;
        margin-top: var(--space-lg);
      }

      .form-footer a {
        color: var(--primary-color);
        font-weight: var(--font-medium);
      }

      .form-footer a:hover {
        color: var(--primary-dark);
      }

      @media (max-width: 480px) {
        .login-container {
          padding: var(--space-lg);
        }
      }
    </style>
  </head>
  <body>
    <div class="login-container">
      <div class="logo">
        <i class="fas fa-shield-alt"></i>
        <h1>VecinoSeguro</h1>
        <p>Control de Acceso Inteligente</p>
      </div>

      <div id="alert-container"></div>

      <form id="loginForm">
        <div class="form-group">
          <label class="form-label" for="email">Correo Electrónico</label>
          <div style="position: relative">
            <i class="fas fa-envelope input-icon"></i>
            <input
              type="email"
              id="email"
              name="email"
              class="form-input"
              required
            />
          </div>
        </div>

        <div class="form-group">
          <label class="form-label" for="password">Contraseña</label>
          <div style="position: relative">
            <i class="fas fa-lock input-icon"></i>
            <input
              type="password"
              id="password"
              name="password"
              class="form-input"
              required
            />
          </div>
        </div>

        <div class="form-group">
          <label class="flex items-center gap-2">
            <input type="checkbox" id="remember" name="remember" />
            <span class="text-secondary">Recordarme</span>
          </label>
        </div>

        <button
          type="submit"
          class="btn btn-primary"
          style="width: 100%"
          id="loginBtn"
        >
          <i class="fas fa-spinner fa-spin hidden" id="loginSpinner"></i>
          <span id="loginText">Iniciar Sesión</span>
        </button>
      </form>

      <div class="form-footer">
        <p>¿No tienes cuenta? <a href="/register">Regístrate aquí</a></p>
      </div>
    </div>

    <script src="/static/js/login.js?v=3.0"></script>
  </body>
</html>
