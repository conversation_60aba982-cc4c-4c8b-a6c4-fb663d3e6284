<!DOCTYPE html>
<html lang="es">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>VecinoSeguro - Control de Acceso</title>
    <link
      href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap"
      rel="stylesheet"
    />
    <link
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
      rel="stylesheet"
    />
    <style>
      :root {
        --primary-color: #4f46e5;
        --primary-dark: #3730a3;
        --secondary-color: #06b6d4;
        --success-color: #10b981;
        --warning-color: #f59e0b;
        --danger-color: #ef4444;
        --gray-50: #f9fafb;
        --gray-100: #f3f4f6;
        --gray-200: #e5e7eb;
        --gray-300: #d1d5db;
        --gray-400: #9ca3af;
        --gray-500: #6b7280;
        --gray-600: #4b5563;
        --gray-700: #374151;
        --gray-800: #1f2937;
        --gray-900: #111827;
        --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
        --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
        --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1),
          0 2px 4px -2px rgb(0 0 0 / 0.1);
        --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1),
          0 4px 6px -4px rgb(0 0 0 / 0.1);
        --border-radius: 0.75rem;
        --border-radius-sm: 0.5rem;
        --border-radius-lg: 1rem;
      }

      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI",
          Roboto, sans-serif;
        background: linear-gradient(
          135deg,
          var(--gray-50) 0%,
          var(--gray-100) 100%
        );
        color: var(--gray-800);
        line-height: 1.6;
        min-height: 100vh;
      }

      .container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 1.5rem;
      }

      /* Navigation Bar */
      .navbar {
        background: white;
        box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
        padding: 1rem 0;
        margin-bottom: 2rem;
        border-radius: 8px;
        position: sticky;
        top: 0;
        z-index: 100;
      }

      .navbar-content {
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 1rem;
        display: flex;
        justify-content: space-between;
        align-items: center;
      }

      .logo {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-size: 1.5rem;
        font-weight: 700;
        color: #4f46e5;
        text-decoration: none;
      }

      .nav-links {
        display: flex;
        gap: 2rem;
        align-items: center;
      }

      .nav-links a {
        text-decoration: none;
        color: #6b7280;
        font-weight: 500;
        padding: 0.5rem 1rem;
        border-radius: 0.5rem;
        transition: all 0.2s;
      }

      .nav-links a:hover,
      .nav-links a.active {
        color: #4f46e5;
        background: #f9fafb;
      }

      .user-menu {
        display: flex;
        align-items: center;
        gap: 1rem;
      }

      .user-menu .nav-links {
        display: flex;
        gap: 1rem;
        align-items: center;
      }

      .user-button {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        background: none;
        border: none;
        cursor: pointer;
        padding: 0.5rem 1rem;
        border-radius: 0.5rem;
        transition: all 0.2s;
        color: #6b7280;
        font-weight: 500;
      }

      .user-button:hover {
        background: #f9fafb;
        color: #4f46e5;
      }

      /* Mobile Navigation */
      .mobile-menu-toggle {
        display: none;
        background: none;
        border: none;
        cursor: pointer;
        padding: 0.5rem;
        color: #6b7280;
        font-size: 1.25rem;
      }

      @media (max-width: 768px) {
        .user-menu .nav-links {
          display: none;
        }

        .mobile-menu-toggle {
          display: block;
        }

        .navbar-content {
          padding: 0 1rem;
        }
      }

      .header {
        background: linear-gradient(
          135deg,
          var(--primary-color) 0%,
          var(--primary-dark) 100%
        );
        color: white;
        padding: 2rem;
        border-radius: var(--border-radius-lg);
        margin-bottom: 2rem;
        box-shadow: var(--shadow-lg);
        position: relative;
        overflow: hidden;
      }

      .header::before {
        content: "";
        position: absolute;
        top: 0;
        right: 0;
        width: 200px;
        height: 200px;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 50%;
        transform: translate(50%, -50%);
      }

      .header-content {
        position: relative;
        z-index: 1;
      }

      .header h1 {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
        display: flex;
        align-items: center;
        gap: 1rem;
      }

      .header p {
        font-size: 1.125rem;
        opacity: 0.9;
        font-weight: 300;
      }

      .stats {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 1.5rem;
        margin-bottom: 2rem;
      }

      .stat-card {
        background: white;
        padding: 2rem;
        border-radius: var(--border-radius);
        box-shadow: var(--shadow);
        border: 1px solid var(--gray-200);
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
      }

      .stat-card:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-lg);
      }

      .stat-card::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        width: 4px;
        height: 100%;
        background: var(--primary-color);
      }

      .stat-header {
        display: flex;
        justify-content: between;
        align-items: center;
        margin-bottom: 1rem;
      }

      .stat-icon {
        width: 3rem;
        height: 3rem;
        border-radius: var(--border-radius-sm);
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        color: white;
        margin-bottom: 1rem;
      }

      .stat-number {
        font-size: 2.5rem;
        font-weight: 700;
        color: var(--gray-900);
        margin-bottom: 0.5rem;
      }

      .stat-label {
        font-size: 0.875rem;
        color: var(--gray-600);
        font-weight: 500;
        text-transform: uppercase;
        letter-spacing: 0.05em;
      }

      .stat-change {
        font-size: 0.875rem;
        font-weight: 500;
        margin-top: 0.5rem;
      }

      .stat-change.positive {
        color: var(--success-color);
      }

      .stat-change.negative {
        color: var(--danger-color);
      }

      .tabs {
        display: flex;
        background: white;
        border-radius: var(--border-radius);
        padding: 0.5rem;
        margin-bottom: 2rem;
        box-shadow: var(--shadow);
        border: 1px solid var(--gray-200);
        gap: 0.25rem;
      }

      .tab {
        flex: 1;
        padding: 1rem 1.5rem;
        background: transparent;
        border: none;
        cursor: pointer;
        border-radius: var(--border-radius-sm);
        font-weight: 500;
        color: var(--gray-600);
        transition: all 0.2s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
        font-size: 0.875rem;
      }

      .tab:hover {
        background: var(--gray-50);
        color: var(--gray-800);
      }

      .tab.active {
        background: var(--primary-color);
        color: white;
        box-shadow: var(--shadow-sm);
      }

      .tab-content {
        background: white;
        padding: 2rem;
        border-radius: var(--border-radius);
        box-shadow: var(--shadow);
        border: 1px solid var(--gray-200);
      }

      .section-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 2rem;
        padding-bottom: 1rem;
        border-bottom: 1px solid var(--gray-200);
      }

      .section-title {
        font-size: 1.5rem;
        font-weight: 600;
        color: var(--gray-900);
        display: flex;
        align-items: center;
        gap: 0.75rem;
      }

      .form-group {
        margin-bottom: 1.5rem;
      }

      .form-group label {
        display: block;
        margin-bottom: 0.5rem;
        font-weight: 500;
        color: var(--gray-700);
        font-size: 0.875rem;
      }

      .form-group input,
      .form-group select {
        width: 100%;
        padding: 0.75rem 1rem;
        border: 1px solid var(--gray-300);
        border-radius: var(--border-radius-sm);
        font-size: 0.875rem;
        transition: all 0.2s ease;
        background: white;
      }

      .form-group input:focus,
      .form-group select:focus {
        outline: none;
        border-color: var(--primary-color);
        box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
      }

      .btn {
        padding: 0.75rem 1.5rem;
        border: none;
        border-radius: var(--border-radius-sm);
        cursor: pointer;
        font-size: 0.875rem;
        font-weight: 500;
        transition: all 0.2s ease;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        text-decoration: none;
      }

      .btn:disabled {
        opacity: 0.5;
        cursor: not-allowed;
      }

      .btn-primary {
        background: var(--primary-color);
        color: white;
      }

      .btn-primary:hover:not(:disabled) {
        background: var(--primary-dark);
        transform: translateY(-1px);
        box-shadow: var(--shadow-md);
      }

      .btn-secondary {
        background: var(--gray-100);
        color: var(--gray-700);
        border: 1px solid var(--gray-300);
      }

      .btn-secondary:hover:not(:disabled) {
        background: var(--gray-200);
      }

      .btn-danger {
        background: var(--danger-color);
        color: white;
      }

      .btn-danger:hover:not(:disabled) {
        background: #dc2626;
        transform: translateY(-1px);
        box-shadow: var(--shadow-md);
      }

      .btn-success {
        background: var(--success-color);
        color: white;
      }

      .btn-success:hover:not(:disabled) {
        background: #059669;
      }

      .table-container {
        overflow-x: auto;
        border-radius: var(--border-radius);
        border: 1px solid var(--gray-200);
      }

      .table {
        width: 100%;
        border-collapse: collapse;
        background: white;
      }

      .table th,
      .table td {
        padding: 1rem;
        text-align: left;
        border-bottom: 1px solid var(--gray-200);
        font-size: 0.875rem;
      }

      .table th {
        background: var(--gray-50);
        font-weight: 600;
        color: var(--gray-700);
        text-transform: uppercase;
        letter-spacing: 0.05em;
        font-size: 0.75rem;
      }

      .table tbody tr:hover {
        background: var(--gray-50);
      }

      .table tbody tr:last-child td {
        border-bottom: none;
      }

      .status-badge {
        padding: 0.25rem 0.75rem;
        border-radius: 9999px;
        font-size: 0.75rem;
        font-weight: 500;
        text-transform: uppercase;
        letter-spacing: 0.05em;
      }

      .status-authorized {
        background: #dcfce7;
        color: #166534;
      }

      .status-denied {
        background: #fee2e2;
        color: #991b1b;
      }

      .location-badge {
        padding: 0.25rem 0.75rem;
        border-radius: 9999px;
        font-size: 0.75rem;
        font-weight: 500;
        text-transform: uppercase;
        letter-spacing: 0.05em;
      }

      .location-entrada {
        background: #dbeafe;
        color: #1e40af;
      }

      .location-salida {
        background: #fef3c7;
        color: #92400e;
      }

      .filter-controls {
        display: flex;
        gap: 1rem;
        align-items: center;
        margin-bottom: 1.5rem;
        flex-wrap: wrap;
      }

      .add-form {
        background: var(--gray-50);
        padding: 2rem;
        border-radius: var(--border-radius);
        border: 1px solid var(--gray-200);
        margin-bottom: 2rem;
      }

      .form-row {
        display: flex;
        gap: 1rem;
        align-items: end;
        flex-wrap: wrap;
      }

      .form-row .form-group {
        flex: 1;
        min-width: 200px;
      }

      .control-panel {
        background: var(--gray-50);
        padding: 2rem;
        border-radius: var(--border-radius);
        border: 1px solid var(--gray-200);
        text-align: center;
        margin-bottom: 2rem;
      }

      .warning-box {
        background: #fef3c7;
        border: 1px solid #fbbf24;
        border-left: 4px solid var(--warning-color);
        padding: 1.5rem;
        border-radius: var(--border-radius);
        color: #92400e;
      }

      .image-link {
        color: var(--primary-color);
        text-decoration: none;
        font-weight: 500;
        display: inline-flex;
        align-items: center;
        gap: 0.25rem;
      }

      .image-link:hover {
        text-decoration: underline;
      }

      .empty-state {
        text-align: center;
        padding: 3rem 2rem;
        color: var(--gray-500);
      }

      .empty-state i {
        font-size: 3rem;
        margin-bottom: 1rem;
        opacity: 0.5;
      }

      @media (max-width: 768px) {
        .container {
          padding: 1rem;
        }

        .header h1 {
          font-size: 2rem;
        }

        .stats {
          grid-template-columns: 1fr;
        }

        .tabs {
          flex-direction: column;
        }

        .form-row {
          flex-direction: column;
        }

        .form-row .form-group {
          min-width: auto;
        }

        .table th,
        .table td {
          padding: 0.75rem 0.5rem;
        }
      }
    </style>
  </head>
  <body>
    <!-- Navigation -->
    <nav class="navbar">
      <div class="navbar-content">
        <a href="/" class="logo">
          <i class="fas fa-shield-alt"></i>
          VecinoSeguro
        </a>
        <div class="user-menu">
          <div class="nav-links">
            <a href="/" class="active">Dashboard</a>
            <a href="/profile">Mi Perfil</a>
          </div>
          <button class="mobile-menu-toggle" onclick="toggleMobileMenu()">
            <i class="fas fa-bars"></i>
          </button>
        </div>
      </div>
    </nav>

    <div class="container">
      <div class="header">
        <div class="header-content">
          <h1><i class="fas fa-shield-alt"></i> VecinoSeguro</h1>
          <p>Sistema inteligente de control de acceso vehicular</p>
        </div>
      </div>

      <div class="stats" id="stats">
        <!-- Estadísticas se cargan aquí -->
      </div>

      <div class="tabs">
        <button class="tab active" onclick="showTab('logs')">
          <i class="fas fa-chart-line"></i> Registros de Acceso
        </button>
        <button class="tab" onclick="showTab('plates')">
          <i class="fas fa-car"></i> Placas Autorizadas
        </button>
        <button class="tab" onclick="showTab('control')">
          <i class="fas fa-cogs"></i> Control Manual
        </button>
      </div>

      <div id="logs-tab" class="tab-content">
        <div class="section-header">
          <h3 class="section-title">
            <i class="fas fa-history"></i>
            Registros de Acceso
          </h3>
          <div class="filter-controls">
            <label
              for="hours-filter"
              style="font-weight: 500; color: var(--gray-700)"
              >Período:</label
            >
            <select id="hours-filter" onchange="loadLogs()" class="form-group">
              <option value="1">Última hora</option>
              <option value="6">Últimas 6 horas</option>
              <option value="24" selected>Últimas 24 horas</option>
              <option value="168">Última semana</option>
            </select>
            <button class="btn btn-secondary" onclick="loadLogs()">
              <i class="fas fa-sync-alt"></i> Actualizar
            </button>
          </div>
        </div>
        <div class="table-container" id="logs-container">
          <!-- Logs se cargan aquí -->
        </div>
      </div>

      <div id="plates-tab" class="tab-content" style="display: none">
        <div class="section-header">
          <h3 class="section-title">
            <i class="fas fa-list-alt"></i>
            Gestión de Placas Autorizadas
          </h3>
        </div>

        <div class="add-form">
          <h4
            style="
              margin-bottom: 1.5rem;
              color: var(--gray-800);
              font-weight: 600;
            "
          >
            <i class="fas fa-plus-circle"></i> Agregar Nueva Placa
          </h4>
          <div class="form-row">
            <div class="form-group">
              <label for="new-plate">Número de Placa:</label>
              <input
                type="text"
                id="new-plate"
                placeholder="ABC1234"
                style="text-transform: uppercase"
                maxlength="8"
              />
            </div>
            <div class="form-group">
              <label for="new-name">Nombre del Propietario:</label>
              <input type="text" id="new-name" placeholder="Juan Pérez" />
            </div>
            <div class="form-group">
              <label for="new-phone">Teléfono (opcional):</label>
              <input type="tel" id="new-phone" placeholder="555-1234" />
            </div>
            <button class="btn btn-primary" onclick="addPlate()">
              <i class="fas fa-plus"></i> Agregar Placa
            </button>
          </div>
        </div>

        <div class="table-container" id="plates-container">
          <!-- Placas se cargan aquí -->
        </div>
      </div>

      <div id="control-tab" class="tab-content" style="display: none">
        <div class="section-header">
          <h3 class="section-title">
            <i class="fas fa-door-open"></i>
            Control Manual del Portón
          </h3>
        </div>

        <div class="control-panel">
          <h4
            style="
              margin-bottom: 1rem;
              color: var(--gray-800);
              font-weight: 600;
            "
          >
            <i class="fas fa-exclamation-triangle"></i> Apertura Manual de
            Emergencia
          </h4>
          <p style="margin-bottom: 2rem; color: var(--gray-600)">
            Use esta función únicamente en casos de emergencia o mantenimiento
            programado
          </p>

          <div style="margin-bottom: 2rem">
            <div class="form-group" style="max-width: 300px; margin: 0 auto">
              <label for="duration">Duración de apertura:</label>
              <select id="duration">
                <option value="5">5 segundos</option>
                <option value="10" selected>10 segundos</option>
                <option value="15">15 segundos</option>
                <option value="20">20 segundos</option>
                <option value="30">30 segundos</option>
              </select>
            </div>
          </div>

          <button
            class="btn btn-danger"
            onclick="openGateManually()"
            style="font-size: 1rem; padding: 1rem 2rem"
          >
            <i class="fas fa-door-open"></i> Abrir Portón Ahora
          </button>
        </div>

        <div class="warning-box">
          <strong
            ><i class="fas fa-exclamation-triangle"></i> Advertencia
            Importante:</strong
          >
          El portón se abrirá inmediatamente al presionar el botón. Asegúrese de
          que el área esté completamente despejada y que no haya vehículos o
          personas en la zona de apertura.
        </div>
      </div>
    </div>

    <script>
      // Cargar datos al iniciar
      loadStats();
      loadLogs();
      loadPlates();

      // Actualizar cada 60 segundos (reducido para evitar rate limiting)
      setInterval(() => {
        loadStats();
        loadLogs();
      }, 60000);

      function showTab(tabName) {
        // Ocultar todas las pestañas
        document
          .querySelectorAll(".tab-content")
          .forEach((tab) => (tab.style.display = "none"));
        document
          .querySelectorAll(".tab")
          .forEach((tab) => tab.classList.remove("active"));

        // Mostrar pestaña seleccionada
        document.getElementById(tabName + "-tab").style.display = "block";
        event.target.classList.add("active");

        if (tabName === "plates") loadPlates();
      }

      async function loadStats() {
        try {
          const response = await fetch("/api/stats");
          const stats = await response.json();

          // Calculate percentages and trends
          const totalAccess = stats.total_accesos || 0;
          const authorizedPercent =
            totalAccess > 0
              ? Math.round((stats.autorizados / totalAccess) * 100)
              : 0;
          const deniedPercent =
            totalAccess > 0
              ? Math.round((stats.no_autorizados / totalAccess) * 100)
              : 0;

          document.getElementById("stats").innerHTML = `
                    <div class="stat-card">
                        <div class="stat-icon" style="background: var(--primary-color);">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <div class="stat-number">${
                          stats.total_accesos || 0
                        }</div>
                        <div class="stat-label">Total de Accesos</div>
                        <div class="stat-change positive">
                            <i class="fas fa-arrow-up"></i> Últimas 24h
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon" style="background: var(--success-color);">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="stat-number">${stats.autorizados || 0}</div>
                        <div class="stat-label">Accesos Autorizados</div>
                        <div class="stat-change positive">
                            <i class="fas fa-percentage"></i> ${authorizedPercent}% del total
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon" style="background: var(--danger-color);">
                            <i class="fas fa-times-circle"></i>
                        </div>
                        <div class="stat-number">${
                          stats.no_autorizados || 0
                        }</div>
                        <div class="stat-label">Accesos Denegados</div>
                        <div class="stat-change negative">
                            <i class="fas fa-percentage"></i> ${deniedPercent}% del total
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon" style="background: var(--secondary-color);">
                            <i class="fas fa-sign-in-alt"></i>
                        </div>
                        <div class="stat-number">${stats.entradas || 0}</div>
                        <div class="stat-label">Entradas Registradas</div>
                        <div class="stat-change">
                            <i class="fas fa-clock"></i> Hoy
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon" style="background: var(--warning-color);">
                            <i class="fas fa-sign-out-alt"></i>
                        </div>
                        <div class="stat-number">${stats.salidas || 0}</div>
                        <div class="stat-label">Salidas Registradas</div>
                        <div class="stat-change">
                            <i class="fas fa-clock"></i> Hoy
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon" style="background: var(--gray-600);">
                            <i class="fas fa-car"></i>
                        </div>
                        <div class="stat-number">${
                          stats.total_placas || 0
                        }</div>
                        <div class="stat-label">Placas Registradas</div>
                        <div class="stat-change">
                            <i class="fas fa-database"></i> En sistema
                        </div>
                    </div>
                `;
        } catch (error) {
          console.error("Error cargando estadísticas:", error);
          document.getElementById("stats").innerHTML = `
                    <div class="empty-state">
                        <i class="fas fa-exclamation-triangle"></i>
                        <p>Error al cargar las estadísticas</p>
                    </div>
                `;
        }
      }

      async function loadLogs() {
        try {
          const hours = document.getElementById("hours-filter").value;
          const response = await fetch(`/api/logs?hours=${hours}`);
          const logs = await response.json();

          if (logs.length === 0) {
            document.getElementById("logs-container").innerHTML = `
                        <div class="empty-state">
                            <i class="fas fa-inbox"></i>
                            <h4>No hay registros</h4>
                            <p>No se encontraron registros de acceso en el período seleccionado.</p>
                        </div>
                    `;
            return;
          }

          let html = `
                    <table class="table">
                        <thead>
                            <tr>
                                <th><i class="fas fa-clock"></i> Fecha y Hora</th>
                                <th><i class="fas fa-map-marker-alt"></i> Ubicación</th>
                                <th><i class="fas fa-car"></i> Placa</th>
                                <th><i class="fas fa-user"></i> Propietario</th>
                                <th><i class="fas fa-shield-alt"></i> Estado</th>
                                <th><i class="fas fa-camera"></i> Imagen</th>
                            </tr>
                        </thead>
                        <tbody>
                `;

          logs.forEach((log) => {
            const date = new Date(log.timestamp);
            const formattedDate = date.toLocaleDateString("es-ES", {
              day: "2-digit",
              month: "2-digit",
              year: "numeric",
            });
            const formattedTime = date.toLocaleTimeString("es-ES", {
              hour: "2-digit",
              minute: "2-digit",
              second: "2-digit",
            });

            const location = log.ubicacion || "N/A";
            const plate = log.placa || "Sin placa detectada";
            const owner = log.propietario || "Desconocido";
            const status = log.autorizado ? "Autorizado" : "Denegado";
            const statusClass = log.autorizado
              ? "status-authorized"
              : "status-denied";
            const locationClass =
              location === "entrada" ? "location-entrada" : "location-salida";

            // Construir enlace de imagen usando el nuevo endpoint
            const imageLink = log.imagen
              ? `<a href="/images/${log.imagen}" target="_blank" class="image-link">
                            <i class="fas fa-eye"></i> Ver imagen
                         </a>`
              : `<span style="color: var(--gray-400);">
                            <i class="fas fa-times"></i> Sin imagen
                         </span>`;

            html += `
                        <tr>
                            <td>
                                <div style="font-weight: 500;">${formattedDate}</div>
                                <div style="font-size: 0.75rem; color: var(--gray-500);">${formattedTime}</div>
                            </td>
                            <td>
                                <span class="location-badge ${locationClass}">
                                    <i class="fas fa-${
                                      location === "entrada"
                                        ? "sign-in-alt"
                                        : "sign-out-alt"
                                    }"></i>
                                    ${location.toUpperCase()}
                                </span>
                            </td>
                            <td>
                                <div style="font-weight: 500; font-family: monospace;">${plate}</div>
                            </td>
                            <td>
                                <div style="font-weight: 500;">${owner}</div>
                            </td>
                            <td>
                                <span class="status-badge ${statusClass}">
                                    <i class="fas fa-${
                                      log.autorizado ? "check" : "times"
                                    }"></i>
                                    ${status}
                                </span>
                            </td>
                            <td>${imageLink}</td>
                        </tr>
                    `;
          });

          html += "</tbody></table>";
          document.getElementById("logs-container").innerHTML = html;
        } catch (error) {
          console.error("Error cargando logs:", error);
          document.getElementById("logs-container").innerHTML = `
                    <div class="empty-state">
                        <i class="fas fa-exclamation-triangle"></i>
                        <h4>Error al cargar registros</h4>
                        <p>No se pudieron cargar los registros de acceso. Intente nuevamente.</p>
                    </div>
                `;
        }
      }

      async function loadPlates() {
        try {
          const response = await fetch("/api/plates");
          const plates = await response.json();

          const plateEntries = Object.entries(plates);

          if (plateEntries.length === 0) {
            document.getElementById("plates-container").innerHTML = `
                        <div class="empty-state">
                            <i class="fas fa-car"></i>
                            <h4>No hay placas registradas</h4>
                            <p>Agregue placas autorizadas usando el formulario de arriba.</p>
                        </div>
                    `;
            return;
          }

          let html = `
                    <table class="table">
                        <thead>
                            <tr>
                                <th><i class="fas fa-hashtag"></i> Placa</th>
                                <th><i class="fas fa-user"></i> Propietario</th>
                                <th><i class="fas fa-phone"></i> Teléfono</th>
                                <th><i class="fas fa-calendar"></i> Fecha de Registro</th>
                                <th><i class="fas fa-cogs"></i> Acciones</th>
                            </tr>
                        </thead>
                        <tbody>
                `;

          plateEntries.forEach(([plate, info]) => {
            const registrationDate = info.created_at
              ? new Date(info.created_at).toLocaleDateString("es-ES")
              : "No disponible";

            html += `
                        <tr>
                            <td>
                                <div style="font-weight: 600; font-family: monospace; font-size: 1rem; color: var(--primary-color);">
                                    ${plate}
                                </div>
                            </td>
                            <td>
                                <div style="font-weight: 500;">
                                    ${info.name || "Sin nombre"}
                                </div>
                            </td>
                            <td>
                                <div style="color: var(--gray-600);">
                                    ${info.phone || "No registrado"}
                                </div>
                            </td>
                            <td>
                                <div style="color: var(--gray-600); font-size: 0.875rem;">
                                    ${registrationDate}
                                </div>
                            </td>
                            <td>
                                <button class="btn btn-danger" onclick="deletePlate('${plate}')" title="Eliminar placa">
                                    <i class="fas fa-trash"></i> Eliminar
                                </button>
                            </td>
                        </tr>
                    `;
          });

          html += "</tbody></table>";
          document.getElementById("plates-container").innerHTML = html;
        } catch (error) {
          console.error("Error cargando placas:", error);
          document.getElementById("plates-container").innerHTML = `
                    <div class="empty-state">
                        <i class="fas fa-exclamation-triangle"></i>
                        <h4>Error al cargar placas</h4>
                        <p>No se pudieron cargar las placas registradas. Intente nuevamente.</p>
                    </div>
                `;
        }
      }

      async function addPlate() {
        const plate = document
          .getElementById("new-plate")
          .value.trim()
          .toUpperCase();
        const name = document.getElementById("new-name").value.trim();
        const phone = document.getElementById("new-phone").value.trim();

        if (!plate) {
          alert("Por favor ingresa una placa");
          return;
        }

        if (!name) {
          alert("Por favor ingresa el nombre del propietario");
          return;
        }

        // Validate plate format (basic validation)
        const plateRegex = /^[A-Z0-9]{6,8}$/;
        if (!plateRegex.test(plate)) {
          alert(
            "Formato de placa inválido. Use solo letras y números (6-8 caracteres)"
          );
          return;
        }

        try {
          const response = await fetch("/api/plates", {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({ plate, name, phone }),
          });

          if (response.ok) {
            document.getElementById("new-plate").value = "";
            document.getElementById("new-name").value = "";
            document.getElementById("new-phone").value = "";
            loadPlates();
            loadStats();

            // Show success message with better styling
            const successMsg = document.createElement("div");
            successMsg.style.cssText = `
              position: fixed;
              top: 20px;
              right: 20px;
              background: var(--success-color);
              color: white;
              padding: 1rem 1.5rem;
              border-radius: var(--border-radius);
              box-shadow: var(--shadow-lg);
              z-index: 1000;
              font-weight: 500;
            `;
            successMsg.innerHTML =
              '<i class="fas fa-check"></i> Placa agregada exitosamente';
            document.body.appendChild(successMsg);

            setTimeout(() => {
              successMsg.remove();
            }, 3000);
          } else {
            const errorData = await response.json();
            alert(`Error: ${errorData.error || "Error agregando placa"}`);
          }
        } catch (error) {
          console.error("Error:", error);
          alert("Error de conexión. Verifique su conexión a internet.");
        }
      }

      async function deletePlate(plate) {
        if (
          !confirm(
            `¿Estás seguro de eliminar la placa ${plate}?\n\nEsta acción no se puede deshacer.`
          )
        )
          return;

        try {
          const response = await fetch(`/api/plates/${plate}`, {
            method: "DELETE",
          });

          if (response.ok) {
            loadPlates();
            loadStats();

            // Show success message
            const successMsg = document.createElement("div");
            successMsg.style.cssText = `
              position: fixed;
              top: 20px;
              right: 20px;
              background: var(--danger-color);
              color: white;
              padding: 1rem 1.5rem;
              border-radius: var(--border-radius);
              box-shadow: var(--shadow-lg);
              z-index: 1000;
              font-weight: 500;
            `;
            successMsg.innerHTML =
              '<i class="fas fa-trash"></i> Placa eliminada exitosamente';
            document.body.appendChild(successMsg);

            setTimeout(() => {
              successMsg.remove();
            }, 3000);
          } else {
            const errorData = await response.json();
            alert(`Error: ${errorData.error || "Error eliminando placa"}`);
          }
        } catch (error) {
          console.error("Error:", error);
          alert("Error de conexión. Verifique su conexión a internet.");
        }
      }

      async function openGateManually() {
        const duration = parseInt(document.getElementById("duration").value);

        if (
          !confirm(
            `⚠️ CONFIRMACIÓN DE SEGURIDAD\n\n¿Está completamente seguro de abrir el portón por ${duration} segundos?\n\nAsegúrese de que:\n• El área esté completamente despejada\n• No haya vehículos o personas en la zona\n• Sea seguro proceder con la apertura`
          )
        ) {
          return;
        }

        // Disable button during operation
        const button = event.target;
        const originalText = button.innerHTML;
        button.disabled = true;
        button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Abriendo...';

        try {
          const response = await fetch("/api/open-gate", {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({ duration }),
          });

          const result = await response.json();

          if (response.ok) {
            // Show success message
            const successMsg = document.createElement("div");
            successMsg.style.cssText = `
              position: fixed;
              top: 20px;
              right: 20px;
              background: var(--success-color);
              color: white;
              padding: 1rem 1.5rem;
              border-radius: var(--border-radius);
              box-shadow: var(--shadow-lg);
              z-index: 1000;
              font-weight: 500;
            `;
            successMsg.innerHTML = `<i class="fas fa-check"></i> ${result.message}`;
            document.body.appendChild(successMsg);

            setTimeout(() => {
              successMsg.remove();
            }, 5000);

            loadLogs(); // Actualizar logs
          } else {
            alert(`❌ Error: ${result.error}`);
          }
        } catch (error) {
          console.error("Error:", error);
          alert("❌ Error de conexión. Verifique su conexión a internet.");
        } finally {
          // Re-enable button
          button.disabled = false;
          button.innerHTML = originalText;
        }
      }

      // Logout function
      function logout() {
        if (confirm("¿Estás seguro de que quieres cerrar sesión?")) {
          localStorage.removeItem("authToken");
          window.location.href = "/login";
        }
      }

      // Mobile menu toggle (placeholder for future mobile menu implementation)
      function toggleMobileMenu() {
        // For now, just show the user menu options in an alert
        // In a full implementation, this would show/hide a mobile menu
        const options = "Dashboard\nMi Perfil\nCerrar Sesión";
        alert("Menú:\n" + options);
      }
    </script>
  </body>
</html>
