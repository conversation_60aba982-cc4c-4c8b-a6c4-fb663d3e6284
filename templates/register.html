<!DOCTYPE html>
<html lang="es">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>VecinoSeguro - Registro</title>
    <link
      href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap"
      rel="stylesheet"
    />
    <link
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
      rel="stylesheet"
    />
    <link
      rel="stylesheet"
      href="{{ url_for('static', filename='css/style.css') }}"
    />
    <style>
      /* Register-specific styles */
      body {
        background: linear-gradient(
          135deg,
          var(--primary-color) 0%,
          var(--secondary-color) 100%
        );
        min-height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: var(--space-lg);
      }

      .register-container {
        background: var(--bg-primary);
        border-radius: var(--radius-xl);
        box-shadow: var(--shadow-xl);
        padding: var(--space-2xl);
        width: 100%;
        max-width: 500px;
      }

      .logo {
        text-align: center;
        margin-bottom: var(--space-2xl);
      }

      .logo i {
        font-size: 3rem;
        color: var(--primary-color);
        margin-bottom: var(--space-md);
      }

      .logo h1 {
        color: var(--text-primary);
        margin-bottom: var(--space-sm);
      }

      .logo p {
        color: var(--text-secondary);
        margin: 0;
      }

      .form-row {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: var(--space-md);
      }

      .form-footer {
        text-align: center;
        margin-top: var(--space-lg);
      }

      .form-footer a {
        color: var(--primary-color);
        font-weight: var(--font-medium);
      }

      .form-footer a:hover {
        color: var(--primary-dark);
      }

      @media (max-width: 640px) {
        .register-container {
          padding: var(--space-lg);
        }

        .form-row {
          grid-template-columns: 1fr;
        }
      }
    </style>
  </head>
  <body>
    <div class="register-container">
      <div class="logo">
        <i class="fas fa-shield-alt"></i>
        <h1>VecinoSeguro</h1>
        <p>Crear Nueva Cuenta</p>
      </div>

      <div id="alert-container"></div>

      <form id="registerForm">
        <div class="form-row">
          <div class="form-group">
            <label for="first_name">Nombre</label>
            <input type="text" id="first_name" name="first_name" required />
          </div>
          <div class="form-group">
            <label for="last_name">Apellido</label>
            <input type="text" id="last_name" name="last_name" required />
          </div>
        </div>

        <div class="form-group">
          <label for="email">Correo Electrónico</label>
          <input type="email" id="email" name="email" required />
        </div>

        <div class="form-group">
          <label for="phone">Teléfono (Opcional)</label>
          <input type="tel" id="phone" name="phone" />
        </div>

        <div class="form-group">
          <label for="password">Contraseña</label>
          <input type="password" id="password" name="password" required />
          <div class="password-strength">
            <div class="strength-bar">
              <div class="strength-fill" id="strengthFill"></div>
            </div>
            <span id="strengthText">Ingresa una contraseña</span>
          </div>
        </div>

        <div class="form-group">
          <label for="password_confirm">Confirmar Contraseña</label>
          <input
            type="password"
            id="password_confirm"
            name="password_confirm"
            required
          />
        </div>

        <button type="submit" class="btn btn-primary" id="registerBtn">
          <i class="fas fa-spinner loading" id="registerSpinner"></i>
          <span id="registerText">Crear Cuenta</span>
        </button>
      </form>

      <div class="login-link">
        <p>¿Ya tienes cuenta? <a href="/login">Inicia sesión aquí</a></p>
      </div>
    </div>

    <script src="{{ url_for('static', filename='js/register.js') }}"></script>
    <script>
      // This script block will be removed
      if (false) {
        const registerForm = document.getElementById("registerForm");
        const registerBtn = document.getElementById("registerBtn");
        const registerSpinner = document.getElementById("registerSpinner");
        const registerText = document.getElementById("registerText");
        const alertContainer = document.getElementById("alert-container");
        const passwordInput = document.getElementById("password");
        const passwordConfirmInput =
          document.getElementById("password_confirm");
        const strengthFill = document.getElementById("strengthFill");
        const strengthText = document.getElementById("strengthText");

        function showAlert(message, type = "error") {
          const alertClass = type === "error" ? "alert-error" : "alert-success";
          alertContainer.innerHTML = `
                    <div class="alert ${alertClass}">
                        <i class="fas fa-${
                          type === "error"
                            ? "exclamation-circle"
                            : "check-circle"
                        }"></i>
                        ${message}
                    </div>
                `;
        }

        function setLoading(loading) {
          registerBtn.disabled = loading;
          if (loading) {
            registerSpinner.classList.add("show");
            registerText.textContent = "Creando cuenta...";
          } else {
            registerSpinner.classList.remove("show");
            registerText.textContent = "Crear Cuenta";
          }
        }

        function checkPasswordStrength(password) {
          let strength = 0;
          let text = "Muy débil";
          let className = "strength-weak";

          if (password.length >= 8) strength++;
          if (/[a-z]/.test(password)) strength++;
          if (/[A-Z]/.test(password)) strength++;
          if (/[0-9]/.test(password)) strength++;
          if (/[^A-Za-z0-9]/.test(password)) strength++;

          switch (strength) {
            case 0:
            case 1:
              text = "Muy débil";
              className = "strength-weak";
              break;
            case 2:
              text = "Débil";
              className = "strength-fair";
              break;
            case 3:
              text = "Buena";
              className = "strength-good";
              break;
            case 4:
            case 5:
              text = "Fuerte";
              className = "strength-strong";
              break;
          }

          strengthFill.className = `strength-fill ${className}`;
          strengthText.textContent = text;

          return strength >= 2;
        }

        passwordInput.addEventListener("input", function () {
          const password = this.value;
          if (password) {
            checkPasswordStrength(password);
          } else {
            strengthFill.className = "strength-fill";
            strengthText.textContent = "Ingresa una contraseña";
          }
        });

        passwordConfirmInput.addEventListener("input", function () {
          const password = passwordInput.value;
          const confirmPassword = this.value;

          if (confirmPassword && password !== confirmPassword) {
            this.classList.add("error");
          } else {
            this.classList.remove("error");
          }
        });

        registerForm.addEventListener("submit", async function (e) {
          e.preventDefault();

          const formData = new FormData(registerForm);
          const data = {
            email: formData.get("email"),
            first_name: formData.get("first_name"),
            last_name: formData.get("last_name"),
            phone: formData.get("phone"),
            password: formData.get("password"),
            password_confirm: formData.get("password_confirm"),
          };

          // Validate passwords match
          if (data.password !== data.password_confirm) {
            showAlert("Las contraseñas no coinciden");
            return;
          }

          // Check password strength
          if (!checkPasswordStrength(data.password)) {
            showAlert("La contraseña debe ser más fuerte");
            return;
          }

          setLoading(true);
          alertContainer.innerHTML = "";

          try {
            const response = await fetch("/auth/register", {
              method: "POST",
              headers: {
                "Content-Type": "application/json",
              },
              body: JSON.stringify(data),
            });

            const result = await response.json();

            if (response.ok && result.success) {
              showAlert(
                "Cuenta creada exitosamente. Redirigiendo al login...",
                "success"
              );

              setTimeout(() => {
                window.location.href = "/login";
              }, 2000);
            } else {
              showAlert(result.error || "Error al crear la cuenta");
            }
          } catch (error) {
            console.error("Registration error:", error);
            showAlert("Error de conexión. Por favor intenta de nuevo.");
          } finally {
            setLoading(false);
          }
      } // End of removed script block
    </script>
  </body>
</html>
