<!DOCTYPE html>
<html lang="es">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Panel de Administración - VecinoSeguro</title>
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css"
    />
    <link
      rel="stylesheet"
      href="{{ url_for('static', filename='css/style.css') }}"
    />
    <style>
      /* Admin Dashboard specific styles */
      body {
        background-color: var(--bg-secondary);
        margin: 0;
        padding: 0;
        min-height: 100vh;
        display: flex;
        flex-direction: column;
      }
      .admin-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
      }

      .admin-header {
        margin-bottom: 30px;
        padding-bottom: 20px;
        border-bottom: 2px solid #e0e0e0;
      }

      .admin-title {
        color: #2c3e50;
        margin: 0 0 10px 0;
      }

      .admin-subtitle {
        color: #7f8c8d;
        margin: 0;
        font-size: 16px;
      }

      .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 20px;
        margin-bottom: 30px;
      }

      .stat-card {
        background: white;
        border-radius: 12px;
        padding: 24px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        border-left: 4px solid #3498db;
        transition: transform 0.2s, box-shadow 0.2s;
      }

      .stat-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 15px rgba(0, 0, 0, 0.15);
      }

      .stat-card.users {
        border-left-color: #3498db;
      }

      .stat-card.vehicles {
        border-left-color: #2ecc71;
      }

      .stat-card.locations {
        border-left-color: #f39c12;
      }

      .stat-card.access {
        border-left-color: #e74c3c;
      }

      .stat-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
      }

      .stat-title {
        color: #2c3e50;
        font-size: 14px;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
      }

      .stat-icon {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 18px;
      }

      .stat-icon.users {
        background: #3498db;
      }

      .stat-icon.vehicles {
        background: #2ecc71;
      }

      .stat-icon.locations {
        background: #f39c12;
      }

      .stat-icon.access {
        background: #e74c3c;
      }

      .stat-value {
        font-size: 32px;
        font-weight: 700;
        color: #2c3e50;
        margin-bottom: 8px;
      }

      .stat-label {
        color: #7f8c8d;
        font-size: 14px;
        margin-bottom: 12px;
      }

      .stat-details {
        display: flex;
        gap: 15px;
        flex-wrap: wrap;
      }

      .stat-detail {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 8px 12px;
        background: #f8f9fa;
        border-radius: 8px;
        min-width: 60px;
      }

      .stat-detail-value {
        font-size: 18px;
        font-weight: 600;
        color: #2c3e50;
      }

      .stat-detail-label {
        font-size: 12px;
        color: #7f8c8d;
        text-transform: uppercase;
        letter-spacing: 0.5px;
      }

      .quick-actions {
        background: white;
        border-radius: 12px;
        padding: 24px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        margin-bottom: 30px;
      }

      .quick-actions-title {
        color: #2c3e50;
        margin: 0 0 20px 0;
        font-size: 18px;
        font-weight: 600;
      }

      .actions-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 15px;
      }

      .action-button {
        display: flex;
        align-items: center;
        gap: 12px;
        padding: 16px 20px;
        background: #f8f9fa;
        border: 1px solid #e0e0e0;
        border-radius: 8px;
        text-decoration: none;
        color: #2c3e50;
        transition: all 0.2s;
        font-weight: 500;
      }

      .action-button:hover {
        background: #e9ecef;
        border-color: #3498db;
        color: #3498db;
        transform: translateY(-1px);
      }

      .action-icon {
        width: 24px;
        height: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #3498db;
        color: white;
        border-radius: 6px;
        font-size: 12px;
      }

      .recent-activity {
        background: white;
        border-radius: 12px;
        padding: 24px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
      }

      .activity-title {
        color: #2c3e50;
        margin: 0 0 20px 0;
        font-size: 18px;
        font-weight: 600;
      }

      .activity-list {
        list-style: none;
        padding: 0;
        margin: 0;
      }

      .activity-item {
        display: flex;
        align-items: center;
        gap: 15px;
        padding: 12px 0;
        border-bottom: 1px solid #f0f0f0;
      }

      .activity-item:last-child {
        border-bottom: none;
      }

      .activity-icon {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #e9ecef;
        color: #6c757d;
        font-size: 14px;
      }

      .activity-content {
        flex: 1;
      }

      .activity-text {
        color: #2c3e50;
        margin: 0 0 4px 0;
        font-size: 14px;
      }

      .activity-time {
        color: #7f8c8d;
        font-size: 12px;
        margin: 0;
      }

      .loading {
        text-align: center;
        padding: 40px;
        color: #7f8c8d;
      }

      .error-message {
        background: #f8d7da;
        color: #721c24;
        padding: 12px 16px;
        border-radius: 8px;
        margin-bottom: 20px;
        border: 1px solid #f5c6cb;
      }

      @media (max-width: 768px) {
        .admin-container {
          padding: 15px;
        }

        .stats-grid {
          grid-template-columns: 1fr;
          gap: 15px;
        }

        .actions-grid {
          grid-template-columns: 1fr;
        }

        .stat-details {
          justify-content: center;
        }

        .stat-card {
          padding: 20px;
        }
      }
    </style>
  </head>
  <body>
    <!-- Navigation -->
    <nav class="navbar">
      <div class="nav-container">
        <div class="nav-logo">
          <h2>VecinoSeguro</h2>
        </div>

        <div class="user-menu">
          <div class="nav-links">
            <a href="/" class="nav-link">Dashboard</a>
            <a href="/profile" class="nav-link">Mi Perfil</a>
            <a href="/admin/dashboard" class="nav-link active">Admin</a>
            <a href="/admin/users" class="nav-link">Usuarios</a>
          </div>
          <div class="hamburger-menu">
            <i class="fas fa-bars"></i>
          </div>
          <button class="user-button" onclick="logout()">
            <i class="fas fa-sign-out-alt"></i>
            Cerrar Sesión
          </button>
        </div>
      </div>
    </nav>

    <!-- Main Content -->
    <div class="admin-container">
      <div class="admin-header">
        <h1 class="admin-title">
          <i class="fas fa-tachometer-alt"></i>
          Panel de Administración
        </h1>
        <p class="admin-subtitle">
          Resumen del sistema y estadísticas generales
        </p>
      </div>

      <!-- Error Message -->
      <div id="errorMessage" class="error-message" style="display: none">
        Error al cargar las estadísticas del sistema.
      </div>

      <!-- Loading Indicator -->
      <div id="loadingIndicator" class="loading">
        <i class="fas fa-spinner fa-spin"></i>
        Cargando estadísticas...
      </div>

      <!-- Statistics Grid -->
      <div id="statsContainer" class="stats-grid" style="display: none">
        <!-- Users Stats -->
        <div class="stat-card users">
          <div class="stat-header">
            <span class="stat-title">Usuarios</span>
            <div class="stat-icon users">
              <i class="fas fa-users"></i>
            </div>
          </div>
          <div class="stat-value" id="totalUsers">0</div>
          <div class="stat-label">Total de usuarios registrados</div>
          <div class="stat-details">
            <div class="stat-detail">
              <div class="stat-detail-value" id="activeUsers">0</div>
              <div class="stat-detail-label">Activos</div>
            </div>
            <div class="stat-detail">
              <div class="stat-detail-value" id="inactiveUsers">0</div>
              <div class="stat-detail-label">Inactivos</div>
            </div>
          </div>
        </div>

        <!-- Vehicles Stats -->
        <div class="stat-card vehicles">
          <div class="stat-header">
            <span class="stat-title">Vehículos</span>
            <div class="stat-icon vehicles">
              <i class="fas fa-car"></i>
            </div>
          </div>
          <div class="stat-value" id="totalVehicles">0</div>
          <div class="stat-label">Vehículos registrados</div>
          <div class="stat-details">
            <div class="stat-detail">
              <div class="stat-detail-value" id="activeVehicles">0</div>
              <div class="stat-detail-label">Activos</div>
            </div>
          </div>
        </div>

        <!-- Locations Stats -->
        <div class="stat-card locations">
          <div class="stat-header">
            <span class="stat-title">Ubicaciones</span>
            <div class="stat-icon locations">
              <i class="fas fa-map-marker-alt"></i>
            </div>
          </div>
          <div class="stat-value" id="totalLocations">0</div>
          <div class="stat-label">Ubicaciones configuradas</div>
          <div class="stat-details">
            <div class="stat-detail">
              <div class="stat-detail-value" id="activeLocations">0</div>
              <div class="stat-detail-label">Activas</div>
            </div>
          </div>
        </div>

        <!-- Access Logs Stats -->
        <div class="stat-card access">
          <div class="stat-header">
            <span class="stat-title">Accesos (24h)</span>
            <div class="stat-icon access">
              <i class="fas fa-door-open"></i>
            </div>
          </div>
          <div class="stat-value" id="totalAccess">0</div>
          <div class="stat-label">Intentos de acceso</div>
          <div class="stat-details">
            <div class="stat-detail">
              <div class="stat-detail-value" id="successfulAccess">0</div>
              <div class="stat-detail-label">Exitosos</div>
            </div>
            <div class="stat-detail">
              <div class="stat-detail-value" id="failedAccess">0</div>
              <div class="stat-detail-label">Fallidos</div>
            </div>
          </div>
        </div>
      </div>

      <!-- Quick Actions -->
      <div class="quick-actions" id="quickActions" style="display: none">
        <h2 class="quick-actions-title">Acciones Rápidas</h2>
        <div class="actions-grid">
          <a href="/admin/users" class="action-button">
            <div class="action-icon">
              <i class="fas fa-users"></i>
            </div>
            Gestionar Usuarios
          </a>
          <a href="/profile" class="action-button">
            <div class="action-icon">
              <i class="fas fa-car"></i>
            </div>
            Ver Vehículos
          </a>
          <a href="/" class="action-button">
            <div class="action-icon">
              <i class="fas fa-chart-line"></i>
            </div>
            Ver Dashboard
          </a>
          <a href="/admin/users" class="action-button">
            <div class="action-icon">
              <i class="fas fa-plus"></i>
            </div>
            Crear Usuario
          </a>
        </div>
      </div>

      <!-- Recent Activity -->
      <div class="recent-activity" id="recentActivity" style="display: none">
        <h2 class="activity-title">Actividad Reciente</h2>
        <ul class="activity-list" id="activityList">
          <!-- Activity items will be loaded here -->
        </ul>
      </div>
    </div>

    <script src="/static/js/admin_dashboard.js?v=1.0"></script>
  </body>
</html>
