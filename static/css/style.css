/* VecinoSeguro - Unified Styles */

/* CSS Variables for consistent theming */
:root {
  /* Primary Colors */
  --primary-color: #4f46e5;
  --primary-dark: #3730a3;
  --primary-light: #6366f1;

  /* Secondary Colors */
  --secondary-color: #06b6d4;
  --secondary-dark: #0891b2;
  --secondary-light: #22d3ee;

  /* Status Colors */
  --success-color: #10b981;
  --success-dark: #059669;
  --success-light: #34d399;

  --warning-color: #f59e0b;
  --warning-dark: #d97706;
  --warning-light: #fbbf24;

  --danger-color: #ef4444;
  --danger-dark: #dc2626;
  --danger-light: #f87171;

  /* Gray Scale */
  --gray-50: #f9fafb;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;
  --gray-600: #4b5563;
  --gray-700: #374151;
  --gray-800: #1f2937;
  --gray-900: #111827;

  /* Text Colors */
  --text-primary: #1f2937;
  --text-secondary: #6b7280;
  --text-muted: #9ca3af;

  /* Background Colors */
  --bg-primary: #ffffff;
  --bg-secondary: #f9fafb;
  --bg-tertiary: #f3f4f6;

  /* Border Colors */
  --border-light: #e5e7eb;
  --border-medium: #d1d5db;
  --border-dark: #9ca3af;

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1),
    0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04);

  /* Border Radius */
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;

  /* Spacing */
  --space-xs: 0.25rem;
  --space-sm: 0.5rem;
  --space-md: 1rem;
  --space-lg: 1.5rem;
  --space-xl: 2rem;
  --space-2xl: 3rem;

  /* Typography */
  --font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
    sans-serif;
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 1.875rem;
  --font-size-4xl: 2.25rem;

  /* Line Heights */
  --leading-tight: 1.25;
  --leading-normal: 1.5;
  --leading-relaxed: 1.75;

  /* Font Weights */
  --font-normal: 400;
  --font-medium: 500;
  --font-semibold: 600;
  --font-bold: 700;
}

/* Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  font-size: 16px;
  line-height: var(--leading-normal);
}

body {
  font-family: var(--font-family);
  color: var(--text-primary);
  background-color: var(--bg-secondary);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Typography */
h1,
h2,
h3,
h4,
h5,
h6 {
  font-weight: var(--font-semibold);
  line-height: var(--leading-tight);
  color: var(--text-primary);
}

h1 {
  font-size: var(--font-size-3xl);
}
h2 {
  font-size: var(--font-size-2xl);
}
h3 {
  font-size: var(--font-size-xl);
}
h4 {
  font-size: var(--font-size-lg);
}
h5 {
  font-size: var(--font-size-base);
}
h6 {
  font-size: var(--font-size-sm);
}

p {
  margin-bottom: var(--space-md);
  color: var(--text-secondary);
}

a {
  color: var(--primary-color);
  text-decoration: none;
  transition: color 0.2s ease;
}

a:hover {
  color: var(--primary-dark);
}

/* Layout Components */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-md);
}

.container-sm {
  max-width: 640px;
  margin: 0 auto;
  padding: 0 var(--space-md);
}

.container-lg {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 var(--space-md);
}

/* Navigation Styles */
.navbar {
  background: var(--bg-primary);
  border-bottom: 1px solid var(--border-light);
  box-shadow: var(--shadow-sm);
  position: sticky;
  top: 0;
  z-index: 50;
}

.nav-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--space-md) var(--space-lg);
  max-width: 1200px;
  margin: 0 auto;
}

.nav-logo {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
}

.nav-logo h2 {
  color: var(--primary-color);
  font-size: var(--font-size-xl);
  font-weight: var(--font-bold);
  margin: 0;
}

.nav-logo i {
  color: var(--primary-color);
  font-size: var(--font-size-xl);
}

.user-menu {
  display: flex;
  align-items: center;
  gap: var(--space-lg);
}

.nav-links {
  display: flex;
  gap: var(--space-lg);
  align-items: center;
}

.nav-link {
  color: var(--text-secondary);
  font-weight: var(--font-medium);
  padding: var(--space-sm) var(--space-md);
  border-radius: var(--radius-md);
  transition: all 0.2s ease;
  text-decoration: none;
}

.nav-link:hover,
.nav-link.active {
  color: var(--primary-color);
  background-color: var(--gray-50);
}

.user-button {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
  padding: var(--space-sm) var(--space-md);
  background: var(--danger-color);
  color: white;
  border: none;
  border-radius: var(--radius-md);
  font-weight: var(--font-medium);
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.user-button:hover {
  background: var(--danger-dark);
}

.hamburger-menu {
  display: none;
  color: var(--text-secondary);
  font-size: var(--font-size-lg);
  cursor: pointer;
}

/* Card Styles */
.card {
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  overflow: hidden;
}

.card-header {
  padding: var(--space-lg);
  border-bottom: 1px solid var(--border-light);
}

.card-body {
  padding: var(--space-lg);
}

.card-footer {
  padding: var(--space-lg);
  border-top: 1px solid var(--border-light);
  background: var(--bg-secondary);
}

/* Button Styles */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-sm);
  padding: var(--space-sm) var(--space-md);
  font-size: var(--font-size-sm);
  font-weight: var(--font-medium);
  border: 1px solid transparent;
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  white-space: nowrap;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-primary {
  background: var(--primary-color);
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background: var(--primary-dark);
}

.btn-secondary {
  background: var(--gray-100);
  color: var(--text-primary);
  border-color: var(--border-medium);
}

.btn-secondary:hover:not(:disabled) {
  background: var(--gray-200);
}

.btn-success {
  background: var(--success-color);
  color: white;
}

.btn-success:hover:not(:disabled) {
  background: var(--success-dark);
}

.btn-warning {
  background: var(--warning-color);
  color: white;
}

.btn-warning:hover:not(:disabled) {
  background: var(--warning-dark);
}

.btn-danger {
  background: var(--danger-color);
  color: white;
}

.btn-danger:hover:not(:disabled) {
  background: var(--danger-dark);
}

.btn-sm {
  padding: var(--space-xs) var(--space-sm);
  font-size: var(--font-size-xs);
}

.btn-lg {
  padding: var(--space-md) var(--space-lg);
  font-size: var(--font-size-lg);
}

/* Form Styles */
.form-group {
  margin-bottom: var(--space-lg);
}

.form-label {
  display: block;
  margin-bottom: var(--space-sm);
  font-weight: var(--font-medium);
  color: var(--text-primary);
}

.form-input,
.form-select,
.form-textarea {
  width: 100%;
  padding: var(--space-sm) var(--space-md);
  border: 1px solid var(--border-medium);
  border-radius: var(--radius-md);
  font-size: var(--font-size-base);
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.form-error {
  margin-top: var(--space-xs);
  font-size: var(--font-size-sm);
  color: var(--danger-color);
}

/* Responsive Design */
@media (max-width: 768px) {
  .nav-links {
    display: none;
  }

  .hamburger-menu {
    display: block;
  }

  .container,
  .container-sm,
  .container-lg {
    padding: 0 var(--space-sm);
  }

  .nav-container {
    padding: var(--space-sm) var(--space-md);
  }
}

/* Utility Classes */
.text-center {
  text-align: center;
}
.text-left {
  text-align: left;
}
.text-right {
  text-align: right;
}

.font-bold {
  font-weight: var(--font-bold);
}
.font-semibold {
  font-weight: var(--font-semibold);
}
.font-medium {
  font-weight: var(--font-medium);
}

.text-primary {
  color: var(--text-primary);
}
.text-secondary {
  color: var(--text-secondary);
}
.text-muted {
  color: var(--text-muted);
}

.bg-primary {
  background-color: var(--bg-primary);
}
.bg-secondary {
  background-color: var(--bg-secondary);
}

.mb-0 {
  margin-bottom: 0;
}
.mb-1 {
  margin-bottom: var(--space-xs);
}
.mb-2 {
  margin-bottom: var(--space-sm);
}
.mb-3 {
  margin-bottom: var(--space-md);
}
.mb-4 {
  margin-bottom: var(--space-lg);
}
.mb-5 {
  margin-bottom: var(--space-xl);
}

.mt-0 {
  margin-top: 0;
}
.mt-1 {
  margin-top: var(--space-xs);
}
.mt-2 {
  margin-top: var(--space-sm);
}
.mt-3 {
  margin-top: var(--space-md);
}
.mt-4 {
  margin-top: var(--space-lg);
}
.mt-5 {
  margin-top: var(--space-xl);
}

.hidden {
  display: none;
}
.block {
  display: block;
}
.flex {
  display: flex;
}
.inline-flex {
  display: inline-flex;
}

.items-center {
  align-items: center;
}
.justify-center {
  justify-content: center;
}
.justify-between {
  justify-content: space-between;
}

.gap-1 {
  gap: var(--space-xs);
}
.gap-2 {
  gap: var(--space-sm);
}
.gap-3 {
  gap: var(--space-md);
}
.gap-4 {
  gap: var(--space-lg);
}

.rounded {
  border-radius: var(--radius-md);
}
.rounded-lg {
  border-radius: var(--radius-lg);
}
.rounded-full {
  border-radius: 9999px;
}

.shadow {
  box-shadow: var(--shadow-md);
}
.shadow-lg {
  box-shadow: var(--shadow-lg);
}
.shadow-xl {
  box-shadow: var(--shadow-xl);
}

/* Alert and Notification Styles */
.alert {
  padding: var(--space-md);
  border-radius: var(--radius-md);
  margin-bottom: var(--space-md);
  border: 1px solid transparent;
  display: flex;
  align-items: center;
  gap: var(--space-sm);
}

.alert-success {
  background-color: rgba(16, 185, 129, 0.1);
  border-color: var(--success-color);
  color: var(--success-dark);
}

.alert-error {
  background-color: rgba(239, 68, 68, 0.1);
  border-color: var(--danger-color);
  color: var(--danger-dark);
}

.alert-warning {
  background-color: rgba(245, 158, 11, 0.1);
  border-color: var(--warning-color);
  color: var(--warning-dark);
}

.alert-info {
  background-color: rgba(6, 182, 212, 0.1);
  border-color: var(--secondary-color);
  color: var(--secondary-dark);
}

.alert-dismissible {
  position: relative;
  padding-right: 3rem;
}

.alert-close {
  position: absolute;
  top: 50%;
  right: var(--space-md);
  transform: translateY(-50%);
  background: none;
  border: none;
  color: inherit;
  cursor: pointer;
  font-size: var(--font-size-lg);
  opacity: 0.7;
  transition: opacity 0.2s ease;
}

.alert-close:hover {
  opacity: 1;
}

/* Modal Styles */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s ease, visibility 0.3s ease;
}

.modal.show {
  opacity: 1;
  visibility: visible;
}

.modal-content {
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-xl);
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
  transform: scale(0.9);
  transition: transform 0.3s ease;
}

.modal.show .modal-content {
  transform: scale(1);
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--space-lg);
  border-bottom: 1px solid var(--border-light);
}

.modal-title {
  margin: 0;
  color: var(--text-primary);
  font-size: var(--font-size-lg);
  font-weight: var(--font-semibold);
}

.modal-close {
  background: none;
  border: none;
  color: var(--text-secondary);
  font-size: var(--font-size-xl);
  cursor: pointer;
  padding: var(--space-sm);
  border-radius: var(--radius-md);
  transition: all 0.2s ease;
  line-height: 1;
}

.modal-close:hover {
  background: var(--gray-100);
  color: var(--text-primary);
}

.modal-body {
  padding: var(--space-lg);
}

.modal-footer {
  display: flex;
  gap: var(--space-md);
  justify-content: flex-end;
  padding: var(--space-lg);
  border-top: 1px solid var(--border-light);
}

/* Loading Spinner */
.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--space-xl);
  color: var(--text-secondary);
}

.loading i {
  font-size: var(--font-size-2xl);
  margin-bottom: var(--space-md);
}

.spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: var(--space-2xl);
  color: var(--text-secondary);
}

.empty-state i {
  font-size: 3rem;
  margin-bottom: var(--space-md);
  opacity: 0.5;
}

.empty-state h3 {
  margin-bottom: var(--space-sm);
  color: var(--text-primary);
}

.empty-state p {
  margin-bottom: var(--space-lg);
}

/* Status Badges */
.badge {
  display: inline-flex;
  align-items: center;
  padding: var(--space-xs) var(--space-sm);
  border-radius: var(--radius-sm);
  font-size: var(--font-size-xs);
  font-weight: var(--font-semibold);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.badge-success {
  background: rgba(16, 185, 129, 0.1);
  color: var(--success-dark);
}

.badge-danger {
  background: rgba(239, 68, 68, 0.1);
  color: var(--danger-dark);
}

.badge-warning {
  background: rgba(245, 158, 11, 0.1);
  color: var(--warning-dark);
}

.badge-info {
  background: rgba(6, 182, 212, 0.1);
  color: var(--secondary-dark);
}

.badge-secondary {
  background: var(--gray-100);
  color: var(--text-secondary);
}
