/* VecinoSeguro - Shared Navigation Styles */
/* This file ensures consistent navigation across all pages */

/* Navigation Bar */
.navbar {
  background: white;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  padding: 1rem 0;
  margin-bottom: 2rem;
  border-radius: 8px;
  position: sticky;
  top: 0;
  z-index: 100;
}

.navbar-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1.5rem;
  font-weight: 700;
  color: #4f46e5;
  text-decoration: none;
}

.logo:hover {
  color: #3730a3;
}

.nav-links {
  display: flex;
  gap: 2rem;
  align-items: center;
}

.nav-links a {
  text-decoration: none;
  color: #6b7280;
  font-weight: 500;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  transition: all 0.2s;
}

.nav-links a:hover,
.nav-links a.active {
  color: #4f46e5;
  background: #f9fafb;
}

.user-menu {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.user-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: none;
  border: none;
  cursor: pointer;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  transition: all 0.2s;
  color: #6b7280;
  font-weight: 500;
}

.user-button:hover {
  background: #f9fafb;
  color: #4f46e5;
}

/* Mobile Navigation */
.mobile-menu-toggle {
  display: none;
  background: none;
  border: none;
  cursor: pointer;
  padding: 0.5rem;
  color: #6b7280;
  font-size: 1.25rem;
}

.mobile-menu-toggle:hover {
  color: #4f46e5;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .nav-links {
    display: none;
  }
  
  .mobile-menu-toggle {
    display: block;
  }
  
  .navbar-content {
    padding: 0 1rem;
  }
  
  .user-menu {
    gap: 0.5rem;
  }
}

/* Additional mobile styles for very small screens */
@media (max-width: 480px) {
  .logo {
    font-size: 1.25rem;
  }
  
  .navbar {
    margin-bottom: 1rem;
  }
  
  .user-button {
    padding: 0.5rem;
  }
  
  .user-button span {
    display: none; /* Hide text on very small screens */
  }
}
