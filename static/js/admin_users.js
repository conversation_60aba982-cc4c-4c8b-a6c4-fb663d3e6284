/**
 * Admin User Management JavaScript
 * Handles user CRUD operations for admin interface
 */

// Global variables
let currentPage = 1;
let totalPages = 1;
let currentUsers = [];
let editingUserId = null;

// Authentication headers
function getAuthHeaders() {
  const token = localStorage.getItem("authToken");
  return {
    "Content-Type": "application/json",
    Authorization: `Bear<PERSON> ${token}`,
  };
}

// Check authentication and admin privileges
function checkAuthentication() {
  const token = localStorage.getItem("authToken");
  if (!token) {
    window.location.href = "/login";
    return false;
  }
  return true;
}

// Initialize page
document.addEventListener("DOMContentLoaded", function () {
  if (!checkAuthentication()) {
    return;
  }

  loadUsers();
  setupEventListeners();
});

// Setup event listeners
function setupEventListeners() {
  // Search input
  document
    .getElementById("searchInput")
    .addEventListener("input", debounce(filterUsers, 300));

  // Filter selects
  document
    .getElementById("statusFilter")
    .addEventListener("change", filterUsers);
  document.getElementById("roleFilter").addEventListener("change", filterUsers);

  // Modal close on outside click
  window.addEventListener("click", function (event) {
    const modal = document.getElementById("userModal");
    if (event.target === modal) {
      closeUserModal();
    }
  });
}

// Debounce function for search
function debounce(func, wait) {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
}

// Load users from API
async function loadUsers(page = 1) {
  try {
    showLoading(true);

    const searchTerm = document.getElementById("searchInput").value;
    const params = new URLSearchParams({
      page: page,
      per_page: 20,
    });

    if (searchTerm) {
      params.append("search", searchTerm);
    }

    const response = await fetch(`/api/admin/users?${params}`, {
      headers: getAuthHeaders(),
    });

    if (response.status === 401 || response.status === 403) {
      localStorage.removeItem("authToken");
      window.location.href = "/login";
      return;
    }

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}`);
    }

    const data = await response.json();
    currentUsers = data.users;
    currentPage = data.pagination.page;
    totalPages = data.pagination.pages;

    displayUsers(currentUsers);
    updatePagination();
  } catch (error) {
    console.error("Error loading users:", error);
    showError("Error al cargar usuarios");
  } finally {
    showLoading(false);
  }
}

// Display users in table
function displayUsers(users) {
  const tbody = document.getElementById("usersTableBody");
  const tableContainer = document.getElementById("usersTableContainer");
  const emptyState = document.getElementById("emptyState");

  if (!users || users.length === 0) {
    tableContainer.style.display = "none";
    emptyState.style.display = "block";
    return;
  }

  tableContainer.style.display = "block";
  emptyState.style.display = "none";

  tbody.innerHTML = users
    .map(
      (user) => `
    <tr>
      <td>
        <div class="user-info">
          <div class="user-avatar">
            ${user.first_name.charAt(0).toUpperCase()}${user.last_name
        .charAt(0)
        .toUpperCase()}
          </div>
          <div class="user-details">
            <div class="user-name">${escapeHtml(user.full_name)}</div>
            <div class="user-email">${escapeHtml(user.email)}</div>
          </div>
        </div>
      </td>
      <td>${user.phone || "-"}</td>
      <td>
        <span class="status-badge ${
          user.is_active ? "status-active" : "status-inactive"
        }">
          ${user.is_active ? "Activo" : "Inactivo"}
        </span>
      </td>
      <td>
        <span class="role-badge ${user.is_admin ? "role-admin" : "role-user"}">
          ${user.is_admin ? "Admin" : "Usuario"}
        </span>
      </td>
      <td>${formatDate(user.created_at)}</td>
      <td>
        <div class="action-buttons">
          <button class="btn-action btn-edit" onclick="editUser(${
            user.id
          })" title="Editar">
            <i class="fas fa-edit"></i>
          </button>
          <button class="btn-action btn-toggle" onclick="toggleUserStatus(${
            user.id
          })" title="Cambiar estado">
            <i class="fas fa-toggle-${user.is_active ? "on" : "off"}"></i>
          </button>
          <button class="btn-action ${
            user.is_admin ? "btn-demote" : "btn-promote"
          }" onclick="toggleUserRole(${user.id})" title="${
        user.is_admin ? "Quitar admin" : "Hacer admin"
      }">
            <i class="fas fa-${user.is_admin ? "user-minus" : "user-plus"}"></i>
          </button>
          <button class="btn-action btn-delete" onclick="deleteUser(${
            user.id
          })" title="Eliminar">
            <i class="fas fa-trash"></i>
          </button>
        </div>
      </td>
    </tr>
  `
    )
    .join("");
}

// Filter users based on search and filters
function filterUsers() {
  const searchTerm = document.getElementById("searchInput").value.toLowerCase();
  const statusFilter = document.getElementById("statusFilter").value;
  const roleFilter = document.getElementById("roleFilter").value;

  // If we have search term, reload from server
  if (searchTerm) {
    loadUsers(1);
    return;
  }

  // Apply local filters
  let filteredUsers = [...currentUsers];

  if (statusFilter) {
    filteredUsers = filteredUsers.filter((user) => {
      return statusFilter === "active" ? user.is_active : !user.is_active;
    });
  }

  if (roleFilter) {
    filteredUsers = filteredUsers.filter((user) => {
      return roleFilter === "admin" ? user.is_admin : !user.is_admin;
    });
  }

  displayUsers(filteredUsers);
}

// Update pagination controls
function updatePagination() {
  const pagination = document.getElementById("pagination");
  const pageInfo = document.getElementById("pageInfo");
  const prevButton = document.getElementById("prevPage");
  const nextButton = document.getElementById("nextPage");

  if (totalPages <= 1) {
    pagination.style.display = "none";
    return;
  }

  pagination.style.display = "flex";
  pageInfo.textContent = `Página ${currentPage} de ${totalPages}`;
  prevButton.disabled = currentPage <= 1;
  nextButton.disabled = currentPage >= totalPages;
}

// Change page
function changePage(direction) {
  const newPage = currentPage + direction;
  if (newPage >= 1 && newPage <= totalPages) {
    loadUsers(newPage);
  }
}

// Show/hide loading indicator
function showLoading(show) {
  const loading = document.getElementById("loadingIndicator");
  const tableContainer = document.getElementById("usersTableContainer");
  const emptyState = document.getElementById("emptyState");

  if (show) {
    loading.style.display = "block";
    tableContainer.style.display = "none";
    emptyState.style.display = "none";
  } else {
    loading.style.display = "none";
  }
}

// Open create user modal
function openCreateUserModal() {
  editingUserId = null;
  document.getElementById("modalTitle").textContent = "Crear Usuario";
  document.getElementById("userForm").reset();
  document.getElementById("userIsActive").checked = true;
  document.getElementById("userEmailVerified").checked = true;
  document.getElementById("userModal").style.display = "block";
}

// Open edit user modal
async function editUser(userId) {
  try {
    const response = await fetch(`/api/admin/users/${userId}`, {
      headers: getAuthHeaders(),
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}`);
    }

    const data = await response.json();
    const user = data.user;

    editingUserId = userId;
    document.getElementById("modalTitle").textContent = "Editar Usuario";
    document.getElementById("userEmail").value = user.email;
    document.getElementById("userFirstName").value = user.first_name;
    document.getElementById("userLastName").value = user.last_name;
    document.getElementById("userPhone").value = user.phone || "";
    document.getElementById("userPassword").value = "";
    document.getElementById("userPassword").placeholder =
      "Dejar vacío para mantener contraseña actual";
    document.getElementById("userIsActive").checked = user.is_active;
    document.getElementById("userIsAdmin").checked = user.is_admin;
    document.getElementById("userEmailVerified").checked = user.email_verified;

    document.getElementById("userModal").style.display = "block";
  } catch (error) {
    console.error("Error loading user:", error);
    showError("Error al cargar datos del usuario");
  }
}

// Close user modal
function closeUserModal() {
  document.getElementById("userModal").style.display = "none";
  editingUserId = null;
}

// Save user (create or update)
async function saveUser() {
  try {
    const formData = {
      email: document.getElementById("userEmail").value.trim(),
      first_name: document.getElementById("userFirstName").value.trim(),
      last_name: document.getElementById("userLastName").value.trim(),
      phone: document.getElementById("userPhone").value.trim(),
      is_active: document.getElementById("userIsActive").checked,
      is_admin: document.getElementById("userIsAdmin").checked,
      email_verified: document.getElementById("userEmailVerified").checked,
    };

    const password = document.getElementById("userPassword").value.trim();
    if (password || !editingUserId) {
      formData.password = password;
    }

    // Validation
    if (!formData.email || !formData.first_name || !formData.last_name) {
      showError("Por favor completa todos los campos requeridos");
      return;
    }

    if (!editingUserId && !password) {
      showError("La contraseña es requerida para nuevos usuarios");
      return;
    }

    const url = editingUserId
      ? `/api/admin/users/${editingUserId}`
      : "/api/admin/users";
    const method = editingUserId ? "PUT" : "POST";

    const response = await fetch(url, {
      method: method,
      headers: getAuthHeaders(),
      body: JSON.stringify(formData),
    });

    const data = await response.json();

    if (!response.ok) {
      throw new Error(data.error || `HTTP ${response.status}`);
    }

    showSuccess(data.message);
    closeUserModal();
    loadUsers(currentPage);
  } catch (error) {
    console.error("Error saving user:", error);
    showError(error.message || "Error al guardar usuario");
  }
}

// Toggle user status
async function toggleUserStatus(userId) {
  try {
    const response = await fetch(`/api/admin/users/${userId}/toggle-status`, {
      method: "POST",
      headers: getAuthHeaders(),
    });

    const data = await response.json();

    if (!response.ok) {
      throw new Error(data.error || `HTTP ${response.status}`);
    }

    showSuccess(data.message);
    loadUsers(currentPage);
  } catch (error) {
    console.error("Error toggling user status:", error);
    showError(error.message || "Error al cambiar estado del usuario");
  }
}

// Toggle user role (admin/regular user)
async function toggleUserRole(userId) {
  try {
    // First get current user data
    const userResponse = await fetch(`/api/admin/users/${userId}`, {
      headers: getAuthHeaders(),
    });

    if (!userResponse.ok) {
      throw new Error(`HTTP ${userResponse.status}`);
    }

    const userData = await userResponse.json();
    const user = userData.user;
    const newRole = !user.is_admin;

    const confirmMessage = newRole
      ? `¿Estás seguro de que quieres dar privilegios de administrador a ${user.full_name}?`
      : `¿Estás seguro de que quieres quitar privilegios de administrador a ${user.full_name}?`;

    if (!confirm(confirmMessage)) {
      return;
    }

    // Update user role
    const response = await fetch(`/api/admin/users/${userId}`, {
      method: "PUT",
      headers: getAuthHeaders(),
      body: JSON.stringify({ is_admin: newRole }),
    });

    const data = await response.json();

    if (!response.ok) {
      throw new Error(data.error || `HTTP ${response.status}`);
    }

    const successMessage = newRole
      ? `${user.full_name} ahora es administrador`
      : `Se quitaron privilegios de administrador a ${user.full_name}`;

    showSuccess(successMessage);
    loadUsers(currentPage);
  } catch (error) {
    console.error("Error toggling user role:", error);
    showError(error.message || "Error al cambiar rol del usuario");
  }
}

// Delete user
async function deleteUser(userId) {
  if (
    !confirm(
      "¿Estás seguro de que quieres eliminar este usuario? Esta acción no se puede deshacer."
    )
  ) {
    return;
  }

  try {
    const response = await fetch(`/api/admin/users/${userId}`, {
      method: "DELETE",
      headers: getAuthHeaders(),
    });

    const data = await response.json();

    if (!response.ok) {
      throw new Error(data.error || `HTTP ${response.status}`);
    }

    showSuccess(data.message);
    loadUsers(currentPage);
  } catch (error) {
    console.error("Error deleting user:", error);
    showError(error.message || "Error al eliminar usuario");
  }
}

// Utility functions
function escapeHtml(text) {
  const div = document.createElement("div");
  div.textContent = text;
  return div.innerHTML;
}

function formatDate(dateString) {
  if (!dateString) return "-";
  const date = new Date(dateString);
  return date.toLocaleDateString("es-ES", {
    year: "numeric",
    month: "short",
    day: "numeric",
  });
}

function showError(message) {
  // Simple alert for now - could be replaced with a toast notification
  alert("Error: " + message);
}

function showSuccess(message) {
  // Simple alert for now - could be replaced with a toast notification
  alert("Éxito: " + message);
}

// Logout function
function logout() {
  if (confirm("¿Estás seguro de que quieres cerrar sesión?")) {
    localStorage.removeItem("authToken");
    window.location.href = "/login";
  }
}
