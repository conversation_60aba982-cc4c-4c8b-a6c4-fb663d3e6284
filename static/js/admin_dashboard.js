/**
 * Admin Dashboard JavaScript
 * Handles loading and displaying system statistics and admin overview
 */

// Authentication headers
function getAuthHeaders() {
  const token = localStorage.getItem("authToken");
  return {
    "Content-Type": "application/json",
    Authorization: `Bearer ${token}`,
  };
}

// Check authentication and admin privileges
function checkAuthentication() {
  const token = localStorage.getItem("authToken");
  if (!token) {
    window.location.href = "/login";
    return false;
  }
  return true;
}

// Initialize dashboard
document.addEventListener("DOMContentLoaded", function () {
  if (!checkAuthentication()) {
    return;
  }

  loadDashboardData();
  
  // Auto-refresh every 5 minutes
  setInterval(loadDashboardData, 300000);
});

// Load all dashboard data
async function loadDashboardData() {
  try {
    showLoading(true);
    hideError();
    
    // Load statistics
    await loadSystemStats();
    
    // Load recent activity (placeholder for now)
    loadRecentActivity();
    
    // Show all sections
    showDashboardSections();
    
  } catch (error) {
    console.error("Error loading dashboard data:", error);
    showError("Error al cargar los datos del panel de administración");
  } finally {
    showLoading(false);
  }
}

// Load system statistics
async function loadSystemStats() {
  try {
    const response = await fetch("/api/admin/stats", {
      headers: getAuthHeaders(),
    });

    if (response.status === 401 || response.status === 403) {
      localStorage.removeItem("authToken");
      window.location.href = "/login";
      return;
    }

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}`);
    }

    const data = await response.json();
    displaySystemStats(data.stats);
    
  } catch (error) {
    console.error("Error loading system stats:", error);
    throw error;
  }
}

// Display system statistics
function displaySystemStats(stats) {
  // Users statistics
  document.getElementById("totalUsers").textContent = stats.users.total;
  document.getElementById("activeUsers").textContent = stats.users.active;
  document.getElementById("inactiveUsers").textContent = stats.users.inactive;
  
  // Vehicles statistics
  document.getElementById("totalVehicles").textContent = stats.vehicles.total;
  document.getElementById("activeVehicles").textContent = stats.vehicles.total; // Assuming all are active
  
  // Locations statistics
  document.getElementById("totalLocations").textContent = stats.locations.total;
  document.getElementById("activeLocations").textContent = stats.locations.total; // Assuming all are active
  
  // Access logs statistics
  document.getElementById("totalAccess").textContent = stats.access_logs.recent_total;
  document.getElementById("successfulAccess").textContent = stats.access_logs.recent_successful;
  document.getElementById("failedAccess").textContent = stats.access_logs.recent_failed;
}

// Load recent activity (placeholder implementation)
function loadRecentActivity() {
  const activityList = document.getElementById("activityList");
  
  // Sample activity data - in a real implementation, this would come from an API
  const sampleActivities = [
    {
      icon: "fas fa-user-plus",
      text: "Nuevo usuario registrado: Test User",
      time: "Hace 2 horas",
      type: "user"
    },
    {
      icon: "fas fa-car",
      text: "Vehículo agregado: ABC123",
      time: "Hace 4 horas",
      type: "vehicle"
    },
    {
      icon: "fas fa-door-open",
      text: "Acceso exitoso: DASFE21",
      time: "Hace 6 horas",
      type: "access"
    },
    {
      icon: "fas fa-user-edit",
      text: "Usuario actualizado: Admin User2",
      time: "Hace 1 día",
      type: "user"
    },
    {
      icon: "fas fa-shield-alt",
      text: "Sistema iniciado correctamente",
      time: "Hace 2 días",
      type: "system"
    }
  ];
  
  activityList.innerHTML = sampleActivities.map(activity => `
    <li class="activity-item">
      <div class="activity-icon">
        <i class="${activity.icon}"></i>
      </div>
      <div class="activity-content">
        <p class="activity-text">${escapeHtml(activity.text)}</p>
        <p class="activity-time">${escapeHtml(activity.time)}</p>
      </div>
    </li>
  `).join('');
}

// Show/hide loading indicator
function showLoading(show) {
  const loading = document.getElementById("loadingIndicator");
  const statsContainer = document.getElementById("statsContainer");
  const quickActions = document.getElementById("quickActions");
  const recentActivity = document.getElementById("recentActivity");
  
  if (show) {
    loading.style.display = "block";
    statsContainer.style.display = "none";
    quickActions.style.display = "none";
    recentActivity.style.display = "none";
  } else {
    loading.style.display = "none";
  }
}

// Show dashboard sections
function showDashboardSections() {
  document.getElementById("statsContainer").style.display = "grid";
  document.getElementById("quickActions").style.display = "block";
  document.getElementById("recentActivity").style.display = "block";
}

// Show error message
function showError(message) {
  const errorElement = document.getElementById("errorMessage");
  errorElement.textContent = message;
  errorElement.style.display = "block";
}

// Hide error message
function hideError() {
  document.getElementById("errorMessage").style.display = "none";
}

// Utility function to escape HTML
function escapeHtml(text) {
  const div = document.createElement('div');
  div.textContent = text;
  return div.innerHTML;
}

// Logout function
function logout() {
  if (confirm("¿Estás seguro de que quieres cerrar sesión?")) {
    localStorage.removeItem("authToken");
    window.location.href = "/login";
  }
}

// Format numbers with commas
function formatNumber(num) {
  return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
}

// Calculate percentage
function calculatePercentage(part, total) {
  if (total === 0) return 0;
  return Math.round((part / total) * 100);
}

// Add animation to stat cards
function animateStatCards() {
  const statCards = document.querySelectorAll('.stat-card');
  statCards.forEach((card, index) => {
    setTimeout(() => {
      card.style.opacity = '0';
      card.style.transform = 'translateY(20px)';
      card.style.transition = 'opacity 0.5s ease, transform 0.5s ease';
      
      setTimeout(() => {
        card.style.opacity = '1';
        card.style.transform = 'translateY(0)';
      }, 100);
    }, index * 100);
  });
}

// Initialize animations when dashboard loads
document.addEventListener("DOMContentLoaded", function() {
  setTimeout(animateStatCards, 500);
});

// Refresh dashboard data
function refreshDashboard() {
  loadDashboardData();
}

// Export functions for potential use in other scripts
window.adminDashboard = {
  refresh: refreshDashboard,
  loadStats: loadSystemStats,
  showError: showError,
  hideError: hideError
};
