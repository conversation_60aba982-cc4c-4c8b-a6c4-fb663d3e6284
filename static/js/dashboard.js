/**
 * VecinoSeguro Dashboard JavaScript
 * Handles dashboard functionality, stats loading, and user interactions
 */

// Initialize dashboard when DOM is loaded
document.addEventListener("DOMContentLoaded", function () {
  // Check authentication first
  if (!checkAuthentication()) {
    return; // Don't load dashboard if not authenticated
  }

  // Load initial data
  loadStats();
  loadLogs();
  loadPlates();

  // Set up auto-refresh (every 60 seconds)
  setInterval(() => {
    loadStats();
    loadLogs();
  }, 60000);

  // Initialize tab functionality
  initializeTabs();
});

/**
 * Check if user is authenticated
 * @returns {boolean} True if authenticated, false otherwise
 */
function checkAuthentication() {
  const token = localStorage.getItem("authToken");
  if (!token) {
    // No token, redirect to login
    window.location.href = "/login";
    return false;
  }
  return true;
}

/**
 * Initialize tab functionality
 */
function initializeTabs() {
  const tabButtons = document.querySelectorAll(".tab-button");
  const tabPanes = document.querySelectorAll(".tab-pane");

  tabButtons.forEach((button) => {
    button.addEventListener("click", function () {
      const targetTab = this.getAttribute("data-tab");

      // Remove active class from all buttons and panes
      tabButtons.forEach((btn) => btn.classList.remove("active"));
      tabPanes.forEach((pane) => pane.classList.remove("active"));

      // Add active class to clicked button and corresponding pane
      this.classList.add("active");
      document.getElementById(targetTab).classList.add("active");

      // Load data for specific tabs
      if (targetTab === "plates-tab") {
        loadPlates();
      }
    });
  });
}

/**
 * Show specific tab (legacy function for compatibility)
 * @param {string} tabName - Name of the tab to show
 */
function showTab(tabName) {
  // Hide all tab contents
  document.querySelectorAll(".tab-content").forEach((tab) => {
    tab.style.display = "none";
  });
  document.querySelectorAll(".tab").forEach((tab) => {
    tab.classList.remove("active");
  });

  // Show selected tab
  const targetTab = document.getElementById(tabName + "-tab");
  if (targetTab) {
    targetTab.style.display = "block";
  }

  // Add active class to button if event target exists
  if (event && event.target) {
    event.target.classList.add("active");
  }

  // Load specific data
  if (tabName === "plates") {
    loadPlates();
  }
}

/**
 * Load and display system statistics
 */
async function loadStats() {
  try {
    const response = await fetch("/api/stats", {
      headers: getAuthHeaders(),
    });

    if (response.status === 401 || response.status === 403) {
      // Authentication failed, redirect to login
      localStorage.removeItem("authToken");
      window.location.href = "/login";
      return;
    }

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}`);
    }

    const stats = await response.json();

    // Calculate percentages and trends
    const totalAccess = stats.total_accesos || 0;
    const authorizedPercent =
      totalAccess > 0 ? Math.round((stats.autorizados / totalAccess) * 100) : 0;
    const deniedPercent =
      totalAccess > 0
        ? Math.round((stats.no_autorizados / totalAccess) * 100)
        : 0;

    // Update stats display
    updateStatsDisplay(stats, authorizedPercent, deniedPercent);
  } catch (error) {
    console.error("Error loading stats:", error);
    showErrorMessage("Error al cargar estadísticas");
  }
}

/**
 * Update the statistics display
 * @param {Object} stats - Statistics data
 * @param {number} authorizedPercent - Percentage of authorized access
 * @param {number} deniedPercent - Percentage of denied access
 */
function updateStatsDisplay(stats, authorizedPercent, deniedPercent) {
  const statsContainer = document.getElementById("stats");
  if (!statsContainer) return;

  statsContainer.innerHTML = `
        <div class="stat-card">
            <div class="stat-card-header">
                <div class="stat-card-title">Total de Accesos</div>
                <div class="stat-card-icon primary">
                    <i class="fas fa-chart-line"></i>
                </div>
            </div>
            <div class="stat-card-value">${stats.total_accesos || 0}</div>
            <div class="stat-card-label">Últimas 24 horas</div>
        </div>
        <div class="stat-card">
            <div class="stat-card-header">
                <div class="stat-card-title">Accesos Autorizados</div>
                <div class="stat-card-icon success">
                    <i class="fas fa-check-circle"></i>
                </div>
            </div>
            <div class="stat-card-value">${stats.autorizados || 0}</div>
            <div class="stat-card-label">${authorizedPercent}% del total</div>
        </div>
        <div class="stat-card">
            <div class="stat-card-header">
                <div class="stat-card-title">Accesos Denegados</div>
                <div class="stat-card-icon danger">
                    <i class="fas fa-times-circle"></i>
                </div>
            </div>
            <div class="stat-card-value">${stats.no_autorizados || 0}</div>
            <div class="stat-card-label">${deniedPercent}% del total</div>
        </div>
        <div class="stat-card">
            <div class="stat-card-header">
                <div class="stat-card-title">Entradas Registradas</div>
                <div class="stat-card-icon secondary">
                    <i class="fas fa-sign-in-alt"></i>
                </div>
            </div>
            <div class="stat-card-value">${stats.entradas || 0}</div>
            <div class="stat-card-label">Hoy</div>
        </div>
    `;
}

/**
 * Load and display access logs
 */
async function loadLogs() {
  try {
    const hours = document.getElementById("logFilter")?.value || 24;
    const response = await fetch(`/api/logs?hours=${hours}`, {
      headers: getAuthHeaders(),
    });

    if (response.status === 401 || response.status === 403) {
      // Authentication failed, redirect to login
      localStorage.removeItem("authToken");
      window.location.href = "/login";
      return;
    }

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}`);
    }

    const logs = await response.json();
    updateLogsDisplay(logs);
  } catch (error) {
    console.error("Error loading logs:", error);
    showErrorMessage("Error al cargar logs de acceso");
  }
}

/**
 * Update the logs display
 * @param {Array} logs - Array of log entries
 */
function updateLogsDisplay(logs) {
  const logsContainer = document.getElementById("logs");
  if (!logsContainer) return;

  if (!logs || logs.length === 0) {
    logsContainer.innerHTML = `
            <div class="text-center text-gray-500 py-8">
                <i class="fas fa-inbox text-4xl mb-4"></i>
                <p>No hay registros de acceso en el período seleccionado</p>
            </div>
        `;
    return;
  }

  const logsHtml = logs
    .map(
      (log) => `
        <tr>
            <td>${formatDateTime(log.timestamp)}</td>
            <td><strong>${log.placa}</strong></td>
            <td>${log.ubicacion}</td>
            <td>
                <span class="badge ${
                  log.autorizado ? "badge-success" : "badge-danger"
                }">
                    <i class="fas fa-${log.autorizado ? "check" : "times"}"></i>
                    ${log.autorizado ? "Autorizado" : "Denegado"}
                </span>
            </td>
            <td>${log.propietario || "N/A"}</td>
            <td>
                ${
                  log.imagen
                    ? `<a href="/images/${log.imagen}" target="_blank" class="btn btn-sm btn-secondary">Ver</a>`
                    : "N/A"
                }
            </td>
        </tr>
    `
    )
    .join("");

  logsContainer.innerHTML = `
        <div class="table-container">
            <table class="table">
                <thead>
                    <tr>
                        <th>Fecha/Hora</th>
                        <th>Placa</th>
                        <th>Ubicación</th>
                        <th>Estado</th>
                        <th>Propietario</th>
                        <th>Imagen</th>
                    </tr>
                </thead>
                <tbody>
                    ${logsHtml}
                </tbody>
            </table>
        </div>
    `;
}

/**
 * Load and display license plates
 */
async function loadPlates() {
  try {
    const response = await fetch("/api/plates", {
      headers: getAuthHeaders(),
    });

    if (response.status === 401 || response.status === 403) {
      // Authentication failed, redirect to login
      localStorage.removeItem("authToken");
      window.location.href = "/login";
      return;
    }

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}`);
    }

    const plates = await response.json();
    updatePlatesDisplay(plates);
  } catch (error) {
    console.error("Error loading plates:", error);
    showErrorMessage("Error al cargar placas autorizadas");
  }
}

/**
 * Update the plates display
 * @param {Object} plates - Object containing plate data
 */
function updatePlatesDisplay(plates) {
  const platesContainer = document.getElementById("plates-list");
  if (!platesContainer) return;

  const plateEntries = Object.entries(plates);

  if (plateEntries.length === 0) {
    platesContainer.innerHTML = `
            <div class="text-center text-gray-500 py-8">
                <i class="fas fa-car text-4xl mb-4"></i>
                <p>No hay placas autorizadas registradas</p>
                <p class="text-sm">Agrega tu primera placa usando el formulario arriba</p>
            </div>
        `;
    return;
  }

  const platesHtml = plateEntries
    .map(
      ([plate, data]) => `
        <tr>
            <td><strong>${plate}</strong></td>
            <td>${data.name || "N/A"}</td>
            <td>${data.phone || "N/A"}</td>
            <td>${formatDateTime(data.created_at)}</td>
            <td>
                <button onclick="deletePlate('${plate}')" class="btn btn-sm btn-danger">
                    <i class="fas fa-trash"></i>
                    Eliminar
                </button>
            </td>
        </tr>
    `
    )
    .join("");

  platesContainer.innerHTML = `
        <div class="table-container">
            <table class="table">
                <thead>
                    <tr>
                        <th>Placa</th>
                        <th>Propietario</th>
                        <th>Teléfono</th>
                        <th>Fecha Registro</th>
                        <th>Acciones</th>
                    </tr>
                </thead>
                <tbody>
                    ${platesHtml}
                </tbody>
            </table>
        </div>
    `;
}

/**
 * Add a new license plate
 */
async function addPlate() {
  const plate = document.getElementById("newPlate")?.value?.trim();
  const name = document.getElementById("ownerName")?.value?.trim();
  const phone = document.getElementById("ownerPhone")?.value?.trim();

  if (!plate || !name) {
    showErrorMessage("Placa y nombre del propietario son requeridos");
    return;
  }

  // Validate plate format
  if (!/^[A-Z0-9]{6,8}$/i.test(plate)) {
    showErrorMessage(
      "Formato de placa inválido. Use 6-8 caracteres alfanuméricos"
    );
    return;
  }

  try {
    const response = await fetch("/api/plates", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        ...getAuthHeaders(),
      },
      body: JSON.stringify({
        plate: plate.toUpperCase(),
        name: name,
        phone: phone,
      }),
    });

    const result = await response.json();

    if (response.ok && result.success) {
      showSuccessMessage("Placa agregada exitosamente");

      // Clear form
      document.getElementById("newPlate").value = "";
      document.getElementById("ownerName").value = "";
      document.getElementById("ownerPhone").value = "";

      // Reload plates
      loadPlates();
    } else {
      showErrorMessage(result.error || "Error al agregar la placa");
    }
  } catch (error) {
    console.error("Error adding plate:", error);
    showErrorMessage("Error de conexión al agregar la placa");
  }
}

/**
 * Delete a license plate
 * @param {string} plate - License plate to delete
 */
async function deletePlate(plate) {
  if (!confirm(`¿Estás seguro de que quieres eliminar la placa ${plate}?`)) {
    return;
  }

  try {
    const response = await fetch(`/api/plates/${plate}`, {
      method: "DELETE",
      headers: getAuthHeaders(),
    });

    const result = await response.json();

    if (response.ok && result.success) {
      showSuccessMessage("Placa eliminada exitosamente");
      loadPlates(); // Reload plates
    } else {
      showErrorMessage(result.error || "Error al eliminar la placa");
    }
  } catch (error) {
    console.error("Error deleting plate:", error);
    showErrorMessage("Error de conexión al eliminar la placa");
  }
}

/**
 * Open gate manually
 */
async function openGate() {
  const duration = document.getElementById("gateDuration")?.value || 10;
  const button = document.querySelector('button[onclick="openGate()"]');

  if (!button) return;

  // Confirm action
  if (!confirm(`¿Abrir el portón por ${duration} segundos?`)) {
    return;
  }

  const originalText = button.innerHTML;

  try {
    // Disable button and show loading
    button.disabled = true;
    button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Abriendo...';

    const response = await fetch("/api/open-gate", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        ...getAuthHeaders(),
      },
      body: JSON.stringify({
        duration: parseInt(duration),
        reason: "Manual operation from dashboard",
      }),
    });

    const result = await response.json();

    if (response.ok && result.success) {
      showSuccessMessage(`Portón abierto por ${duration} segundos`);
      loadLogs(); // Update logs
    } else {
      showErrorMessage(result.error || "Error al abrir el portón");
    }
  } catch (error) {
    console.error("Error opening gate:", error);
    showErrorMessage("Error de conexión al abrir el portón");
  } finally {
    // Re-enable button
    button.disabled = false;
    button.innerHTML = originalText;
  }
}

/**
 * Logout function
 */
function logout() {
  if (confirm("¿Estás seguro de que quieres cerrar sesión?")) {
    localStorage.removeItem("authToken");
    window.location.href = "/login";
  }
}

/**
 * Utility Functions
 */

/**
 * Get authentication headers
 * @returns {Object} Headers object with authorization
 */
function getAuthHeaders() {
  const token = localStorage.getItem("authToken");
  return token ? { Authorization: `Bearer ${token}` } : {};
}

/**
 * Format date and time for display
 * @param {string} dateString - ISO date string
 * @returns {string} Formatted date and time
 */
function formatDateTime(dateString) {
  if (!dateString) return "N/A";

  try {
    const date = new Date(dateString);
    return date.toLocaleString("es-ES", {
      year: "numeric",
      month: "2-digit",
      day: "2-digit",
      hour: "2-digit",
      minute: "2-digit",
      second: "2-digit",
    });
  } catch (error) {
    return dateString;
  }
}

/**
 * Show success message
 * @param {string} message - Success message to display
 */
function showSuccessMessage(message) {
  const notification = createNotification(message, "success");
  document.body.appendChild(notification);

  setTimeout(() => {
    notification.remove();
  }, 3000);
}

/**
 * Show error message
 * @param {string} message - Error message to display
 */
function showErrorMessage(message) {
  const notification = createNotification(message, "error");
  document.body.appendChild(notification);

  setTimeout(() => {
    notification.remove();
  }, 5000);
}

/**
 * Create notification element
 * @param {string} message - Message to display
 * @param {string} type - Type of notification ('success' or 'error')
 * @returns {HTMLElement} Notification element
 */
function createNotification(message, type) {
  const notification = document.createElement("div");
  notification.className = `alert alert-${
    type === "success" ? "success" : "error"
  }`;
  notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 1000;
        max-width: 400px;
        box-shadow: var(--shadow-lg);
    `;

  notification.innerHTML = `
        <i class="fas fa-${
          type === "success" ? "check-circle" : "exclamation-circle"
        }"></i>
        ${message}
    `;

  return notification;
}

/**
 * Close gate manually
 */
async function closeGate() {
  const button = document.querySelector('button[onclick="closeGate()"]');

  if (!button) return;

  const originalText = button.innerHTML;
  button.disabled = true;
  button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Cerrando...';

  try {
    const response = await fetch("/api/gate/close", {
      method: "POST",
      headers: getAuthHeaders(),
    });

    const data = await response.json();

    if (data.success) {
      showSuccess("Portón cerrado exitosamente");
      updateGateStatus();
    } else {
      throw new Error(data.error || "Error al cerrar el portón");
    }
  } catch (error) {
    console.error("Error closing gate:", error);
    showError("Error al cerrar el portón");
  } finally {
    button.disabled = false;
    button.innerHTML = originalText;
  }
}

/**
 * Emergency stop
 */
async function emergencyStop() {
  if (
    !confirm("¿Estás seguro de que quieres activar la parada de emergencia?")
  ) {
    return;
  }

  const button = document.querySelector('button[onclick="emergencyStop()"]');

  if (!button) return;

  const originalText = button.innerHTML;
  button.disabled = true;
  button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Deteniendo...';

  try {
    const response = await fetch("/api/gate/emergency-stop", {
      method: "POST",
      headers: getAuthHeaders(),
    });

    const data = await response.json();

    if (data.success) {
      showSuccess("Parada de emergencia activada");
      updateGateStatus();
    } else {
      throw new Error(data.error || "Error en parada de emergencia");
    }
  } catch (error) {
    console.error("Error in emergency stop:", error);
    showError("Error en parada de emergencia");
  } finally {
    button.disabled = false;
    button.innerHTML = originalText;
  }
}

/**
 * Update gate status indicator
 */
async function updateGateStatus() {
  try {
    const response = await fetch("/api/gate/status", {
      headers: getAuthHeaders(),
    });

    const data = await response.json();
    const indicator = document.getElementById("gate-status-indicator");

    if (!indicator) return;

    if (data.success && data.status) {
      const status = data.status.toLowerCase();
      indicator.className = `status-indicator ${status}`;

      let icon, text;
      switch (status) {
        case "open":
          icon = "fas fa-door-open";
          text = "Portón Abierto";
          break;
        case "closed":
          icon = "fas fa-door-closed";
          text = "Portón Cerrado";
          break;
        case "opening":
          icon = "fas fa-arrow-up";
          text = "Abriendo...";
          break;
        case "closing":
          icon = "fas fa-arrow-down";
          text = "Cerrando...";
          break;
        default:
          icon = "fas fa-question-circle";
          text = "Estado Desconocido";
      }

      indicator.innerHTML = `<i class="${icon}"></i><span>${text}</span>`;
    } else {
      indicator.className = "status-indicator";
      indicator.innerHTML =
        '<i class="fas fa-exclamation-triangle"></i><span>Error al obtener estado</span>';
    }
  } catch (error) {
    console.error("Error getting gate status:", error);
    const indicator = document.getElementById("gate-status-indicator");
    if (indicator) {
      indicator.className = "status-indicator";
      indicator.innerHTML =
        '<i class="fas fa-exclamation-triangle"></i><span>Error de conexión</span>';
    }
  }
}

/**
 * Show add plate modal
 */
function showAddPlateModal() {
  alert("Funcionalidad de agregar placa en desarrollo");
}
